import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateUserDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/cubits/setCategoryPlayedCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/setContestLeaderboardCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/updateLevelCubit.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/statistic/cubits/updateStatisticCubit.dart';
import 'package:flutterquiz/features/statistic/statisticRepository.dart';
import 'package:flutterquiz/ui/screens/quiz/result/cubits/result_cubit.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/widgets/result_buttons.dart';
import 'package:flutterquiz/ui/screens/quiz/result/widgets/result_content.dart';
import 'package:flutterquiz/ui/screens/quiz/result/widgets/result_header.dart';
import 'package:flutterquiz/ui/screens/quiz/result/widgets/result_error_handler.dart';
import 'package:flutterquiz/ui/screens/subscription/paywall.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/pre_result_ad_dialog.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/rating_service.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:screenshot/screenshot.dart';

/// شاشة النتائج المحسنة مع بنية معمارية أفضل
class ResultScreenImproved extends StatefulWidget {
  final ResultData resultData;

  const ResultScreenImproved({
    super.key,
    required this.resultData,
  });

  /// إنشاء Route للشاشة
  static Route<dynamic> route(RouteSettings routeSettings) {
    final args = routeSettings.arguments! as Map<String, dynamic>;
    final resultData = ResultData.fromArguments(args);

    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<UpdateLevelCubit>(
            create: (_) => UpdateLevelCubit(QuizRepository()),
          ),
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
          BlocProvider<UpdateStatisticCubit>(
            create: (_) => UpdateStatisticCubit(StatisticRepository()),
          ),
          BlocProvider<SetContestLeaderboardCubit>(
            create: (_) => SetContestLeaderboardCubit(QuizRepository()),
          ),
          BlocProvider<SetCategoryPlayed>(
            create: (_) => SetCategoryPlayed(QuizRepository()),
          ),
          BlocProvider<UpdateUserDetailCubit>(
            create: (_) => UpdateUserDetailCubit(ProfileManagementRepository()),
          ),
        ],
        child: ResultScreenImproved(resultData: resultData),
      ),
    );
  }

  @override
  State<ResultScreenImproved> createState() => _ResultScreenImprovedState();
}

class _ResultScreenImprovedState extends State<ResultScreenImproved> {
  final ScreenshotController screenshotController = ScreenshotController();
  final RatingService _ratingService = RatingService();
  late ResultCubit _resultCubit;

  @override
  void initState() {
    super.initState();

    // إنشاء Result Cubit
    _resultCubit = ResultCubit(
      resultData: widget.resultData,
      context: context,
    );

    // عرض إعلان ما قبل النتائج
    _showPreResultAdIfNeeded();

    // تهيئة النتائج
    _initializeResults();

    // عرض نافذة التقييم
    _checkAndShowRatingPrompt();
  }

  @override
  void dispose() {
    _resultCubit.close();
    super.dispose();
  }

  /// عرض إعلان ما قبل النتائج إذا لزم الأمر
  void _showPreResultAdIfNeeded() {
    if (!widget.resultData.isPremiumCategory) {
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          _showPreResultAdDialog();
        }
      });
    }
  }

  /// تهيئة النتائج
  void _initializeResults() {
    Future.delayed(Duration.zero, () {
      _resultCubit.initializeResults();
    });
  }

  /// عرض نافذة التقييم
  void _checkAndShowRatingPrompt() {
    // عرض نافذة التقييم إذا حقق المستخدم نتيجة جيدة
    if (widget.resultData.quizType != QuizTypes.oneVsOneBattle) {
      // سيتم استدعاء هذا بعد حساب النتائج في ResultCubit
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted && _resultCubit.state.winPercentage >= 70) {
          _ratingService.promptRatingIfNeeded(_resultCubit.state.winPercentage);
        }
      });
    }
  }

  /// عرض نافذة الإعلان قبل النتائج
  void _showPreResultAdDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PreResultAdDialog(
        onContinue: () => Navigator.of(context).pop(),
        onSubscribe: () => _navigateToPaywall(),
      ),
    );
  }

  /// الانتقال لصفحة الاشتراك
  Future<void> _navigateToPaywall() async {
    Navigator.of(context).pop();

    try {
      final offerings = await Purchases.getOfferings();
      if (!mounted) return;

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider<UpdateUserDetailCubit>(
                create: (_) =>
                    UpdateUserDetailCubit(ProfileManagementRepository()),
              ),
              BlocProvider<UserDetailsCubit>(
                create: (_) => UserDetailsCubit(ProfileManagementRepository()),
              ),
            ],
            child: Paywall(offering: offerings.current),
          ),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider<UpdateUserDetailCubit>(
                create: (_) =>
                    UpdateUserDetailCubit(ProfileManagementRepository()),
              ),
              BlocProvider<UserDetailsCubit>(
                create: (_) => UserDetailsCubit(ProfileManagementRepository()),
              ),
            ],
            child: const Paywall(offering: null),
          ),
        ),
      );
    }
  }

  /// تحديث تفاصيل المستخدم
  Future<void> _updateUserDetails() async {
    await context.read<UserDetailsCubit>().fetchUserDetails();
  }

  /// معالجة الرجوع
  void _handleBackNavigation() {
    _updateUserDetails();
    Navigator.pop(context);
  }

  /// مشاركة النتائج
  Future<void> _shareResult() async {
    // تم نقل منطق المشاركة إلى ResultButtons
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop:
          context.read<UserDetailsCubit>().state is! UserDetailsFetchInProgress,
      onPopInvoked: (didPop) {
        if (didPop) return;
        _handleBackNavigation();
      },
      child: BlocProvider.value(
        value: _resultCubit,
        child: MultiBlocListener(
          listeners: [
            // مستمع تحديث النقاط والعملات
            BlocListener<UpdateScoreAndCoinsCubit, UpdateScoreAndCoinsState>(
              listener: (context, state) {
                if (state is UpdateScoreAndCoinsFailure) {
                  if (state.errorMessage == errorCodeUnauthorizedAccess) {
                    _resultCubit.updateAlreadyLoggedInDialogState(true);
                    showAlreadyLoggedInDialog(context);
                  }
                }
              },
            ),

            // مستمع تحديث الإحصائيات
            BlocListener<UpdateStatisticCubit, UpdateStatisticState>(
              listener: (context, state) {
                if (state is UpdateStatisticFailure) {
                  if (state.errorMessageCode == errorCodeUnauthorizedAccess) {
                    _resultCubit.updateAlreadyLoggedInDialogState(true);
                    showAlreadyLoggedInDialog(context);
                  }
                }
              },
            ),

            // مستمع قائمة المتصدرين
            BlocListener<SetContestLeaderboardCubit,
                SetContestLeaderboardState>(
              listener: (context, state) {
                if (state is SetContestLeaderboardFailure) {
                  if (state.errorMessage == errorCodeUnauthorizedAccess) {
                    _resultCubit.updateAlreadyLoggedInDialogState(true);
                    showAlreadyLoggedInDialog(context);
                  }
                }
              },
            ),
          ],
          child: Scaffold(
            extendBodyBehindAppBar: true,
            appBar: _buildAppBar(),
            body: _buildBody(),
          ),
        ),
      ),
    );
  }

  /// بناء شريط التطبيق
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.1),
              Colors.transparent,
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
      ),
      title: BlocBuilder<ResultCubit, ResultStateData>(
        builder: (context, state) {
          return Text(
            state.appbarTitle,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.bold,
              fontSize: 24,
              letterSpacing: 0.5,
              shadows: [
                Shadow(
                  color: Theme.of(context).shadowColor.withOpacity(0.3),
                  offset: const Offset(1, 1),
                  blurRadius: 2,
                ),
              ],
            ),
          );
        },
      ),
      leading: _buildAppBarButton(
        icon: Icons.arrow_back_rounded,
        onPressed: _handleBackNavigation,
      ),
      actions: [
        _buildAppBarButton(
          icon: Icons.share_rounded,
          onPressed: _shareResult,
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  /// بناء زر شريط التطبيق
  Widget _buildAppBarButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return IconButton(
      icon: Container(
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface.withOpacity(
              Theme.of(context).brightness == Brightness.dark ? 0.8 : 0.9),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.onSurface,
          size: 22,
        ),
      ),
      onPressed: onPressed,
    );
  }

  /// بناء الجسم الرئيسي
  Widget _buildBody() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: SafeArea(
        child: BlocBuilder<ResultCubit, ResultStateData>(
          builder: (context, state) {
            if (state.isCalculating) {
              return _buildLoadingState();
            }

            if (state.hasError) {
              return _buildErrorState(
                  state.errorMessage ?? 'حدث خطأ غير متوقع');
            }

            return _buildResultContent(state);
          },
        ),
      ),
    );
  }

  /// بناء حالة التحميل
  Widget _buildLoadingState() {
    return const ResultLoadingWidget(
      message: 'جاري حساب النتائج...',
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(String errorMessage) {
    return ResultErrorHandler(
      errorMessage: errorMessage,
      onRetry: () => _resultCubit.initializeResults(),
      onGoHome: _handleBackNavigation,
    );
  }

  /// بناء محتوى النتائج
  Widget _buildResultContent(ResultStateData state) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // حاوية النتائج الرئيسية
            Screenshot(
              controller: screenshotController,
              child: Container(
                width: MediaQuery.of(context).size.width * 0.95,
                decoration: _buildResultContainerDecoration(),
                padding: const EdgeInsets.all(25),
                child: Column(
                  children: [
                    // رأس النتائج
                    ResultHeader(resultState: state),

                    // محتوى النتائج
                    ResultContent(
                      resultData: widget.resultData,
                      resultState: state,
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 25),

            // أزرار التحكم
            ResultButtons(
              resultData: widget.resultData,
              resultState: state,
              screenshotController: screenshotController,
              onUpdateUserDetails: _updateUserDetails,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تصميم حاوية النتائج
  BoxDecoration _buildResultContainerDecoration() {
    return BoxDecoration(
      gradient: LinearGradient(
        colors: [
          Theme.of(context).colorScheme.surface.withOpacity(
              Theme.of(context).brightness == Brightness.dark ? 0.25 : 0.45),
          Theme.of(context).colorScheme.surface.withOpacity(
              Theme.of(context).brightness == Brightness.dark ? 0.15 : 0.35),
          Theme.of(context).colorScheme.surface.withOpacity(
              Theme.of(context).brightness == Brightness.dark ? 0.05 : 0.25),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(30),
      boxShadow: [
        BoxShadow(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
          blurRadius: 20,
          spreadRadius: 2,
          offset: const Offset(0, 8),
        ),
      ],
      border: Border.all(
        color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
        width: 2,
      ),
    );
  }
}
