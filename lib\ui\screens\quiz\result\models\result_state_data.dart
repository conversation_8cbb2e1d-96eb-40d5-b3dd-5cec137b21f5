/// نموذج لحالة النتائج المحسوبة
class ResultStateData {
  // النتائج الأساسية
  final bool isWinner;
  final int earnedCoins;
  final String? winnerId;
  final double winPercentage;
  
  // رسائل النتائج
  final String mainMessage;
  final String subMessage;
  final String appbarTitle;
  
  // حالة العمليات
  final bool isCalculating;
  final bool hasError;
  final String? errorMessage;
  
  // معلومات إضافية
  final List<Map<String, dynamic>> usersWithRank;
  final bool displayedAlreadyLoggedInDialog;

  const ResultStateData({
    required this.isWinner,
    required this.earnedCoins,
    required this.winPercentage,
    required this.mainMessage,
    required this.subMessage,
    required this.appbarTitle,
    this.winnerId,
    this.isCalculating = false,
    this.hasError = false,
    this.errorMessage,
    this.usersWithRank = const [],
    this.displayedAlreadyLoggedInDialog = false,
  });

  /// إنشاء حالة أولية
  factory ResultStateData.initial() {
    return const ResultStateData(
      isWinner: false,
      earnedCoins: 0,
      winPercentage: 0.0,
      mainMessage: '',
      subMessage: '',
      appbarTitle: '',
      isCalculating: true,
    );
  }

  /// إنشاء حالة خطأ
  factory ResultStateData.error(String errorMessage) {
    return ResultStateData(
      isWinner: false,
      earnedCoins: 0,
      winPercentage: 0.0,
      mainMessage: '',
      subMessage: '',
      appbarTitle: '',
      hasError: true,
      errorMessage: errorMessage,
    );
  }

  /// نسخ البيانات مع تعديل بعض القيم
  ResultStateData copyWith({
    bool? isWinner,
    int? earnedCoins,
    String? winnerId,
    double? winPercentage,
    String? mainMessage,
    String? subMessage,
    String? appbarTitle,
    bool? isCalculating,
    bool? hasError,
    String? errorMessage,
    List<Map<String, dynamic>>? usersWithRank,
    bool? displayedAlreadyLoggedInDialog,
  }) {
    return ResultStateData(
      isWinner: isWinner ?? this.isWinner,
      earnedCoins: earnedCoins ?? this.earnedCoins,
      winnerId: winnerId ?? this.winnerId,
      winPercentage: winPercentage ?? this.winPercentage,
      mainMessage: mainMessage ?? this.mainMessage,
      subMessage: subMessage ?? this.subMessage,
      appbarTitle: appbarTitle ?? this.appbarTitle,
      isCalculating: isCalculating ?? this.isCalculating,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
      usersWithRank: usersWithRank ?? this.usersWithRank,
      displayedAlreadyLoggedInDialog: displayedAlreadyLoggedInDialog ?? this.displayedAlreadyLoggedInDialog,
    );
  }
}

/// مولد الرسائل للنتائج
class ResultMessageGenerator {
  static String getMainMessage(bool isWinner) {
    if (isWinner) {
      final messages = [
        "🎉 أحسنت! لقد أكملت الاختبار بنجاح",
        "🌟 رائع! أداء متميز ومبهر",
        "🏆 ممتاز! لقد حققت نتيجة رائعة",
        "✨ مذهل! استمر في هذا التقدم",
      ];
      return messages[DateTime.now().millisecond % messages.length];
    } else {
      final messages = [
        "💪 حاول مرة أخرى، أنت قادر على تحقيق نتيجة أفضل",
        "🎯 لا بأس، كل محاولة خطوة نحو النجاح",
        "🚀 استمر في المحاولة، النجاح قريب",
        "⭐ لا تستسلم، أنت أقرب للهدف مما تتخيل",
      ];
      return messages[DateTime.now().millisecond % messages.length];
    }
  }

  static String getSubMessage(bool isWinner) {
    if (isWinner) {
      final messages = [
        "استمر في التقدم والتعلم! 📚",
        "أنت على الطريق الصحيح! 🎯",
        "مهاراتك تتطور باستمرار! 🌱",
        "إنجاز رائع يستحق التقدير! 👏",
      ];
      return messages[DateTime.now().millisecond % messages.length];
    } else {
      final messages = [
        "لا تستسلم، كل محاولة تقربك من النجاح 🌟",
        "التعلم رحلة، والأخطاء جزء منها 📖",
        "المثابرة هي مفتاح النجاح 🔑",
        "كل خطوة تجعلك أقوى وأكثر خبرة 💪",
      ];
      return messages[DateTime.now().millisecond % messages.length];
    }
  }
}
