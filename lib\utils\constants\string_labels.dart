//Widget Terms and condition
const andLbl = 'andLbl';
const privacyPolicy = 'privacyPolicy';
const termAgreement = 'termAgreement';
const termOfService = 'termOfService';

//fill otp screen
const countryLbl = 'countryLbl';
const enterNumberLbl = 'enterNumberLbl';
const enterOtp = 'enterOtpMsg';
const noInterNetSnackBar = 'noInterNetSnackBar';
const otpSendLbl = 'otpSendLbl';
const otpVerificationLbl = 'otpVerificationLbl';
const receiveOtpLbl = 'receiveOtpLbl';
const requestOtpLbl = 'requestOtpLbl';
const resendBtn = 'resendBtn';
const resendSnackBar = 'resendSnackBar';
const resetLbl = 'resetLbl';
const submitBtn = 'submitBtn';
const submittingButton = 'submittingButton';
const validMobMsg = 'validMobMsg';

//signInScreen
const emailLbl = 'emailLbl';
const emailRequiredMsg = 'emailRequiredMsg';
const enterEmailLbl = 'enterEmailLbl';
const forgotPwdLbl = 'forgotPwdLbl';
const loginLbl = 'loginLbl';
const loginSocialMediaLbl = 'loginSocialMediaLbl';
const noAccountLbl = 'noAccountLbl';
const orLbl = 'orLbl';
const pwdLbl = 'pwdLbl';
const pwdLengthMsg = 'pwdLengthMsg';
const pwdResetLinkLbl = 'pwdResetLinkLbl';
const resetEnterEmailLbl = 'resetEnterEmailLbl';
const resetPwdLbl = 'resetPwdLbl';
const signUpLbl = 'signUpLbl';
const userLoginLbl = 'userLoginLbl';
const validEmail = 'validEmail';

//SignUpScreen
const alreadyAccountLbl = 'alreadyAccountLbl';
const cnPwdLbl = 'cnPwdLbl';
const cnPwdNotMatchMsg = 'cnPwdNotMatchMsg';
const emailVerify = 'emailVerify';

//battle CreateRoomDialog widget
const creatingLbl = 'creatingLbl'; //use as Uppercase also
const creatingLoadingLbl = 'creatingLoadingLbl';
const enterCodeLbl = 'enterCodeLbl';
const entryLbl = 'entryLbl';
const joinLbl = 'joinLbl'; //use as Uppercase also
const joiningLoadingLbl = 'joiningLoadingLbl';
const liveChatLbl = 'liveChatLbl';

// battle WaitForOthersContainer Widget
const waitOtherComplete = 'waitOtherComplete';

// battle WaitingForPlayerDialog Widget
const entryAmountLbl = 'entryAmountLbl';
const okayLbl = 'okayLbl';
const roomCodeLbl = 'roomCodeLbl';
const roomDeletedOwnerLbl = 'roomDeletedOwnerLbl';
const shareRoomCodeLbl = 'shareRoomCodeLbl';
const startLbl = 'startLbl';
const vsLbl = 'vsLbl';
const waitingLbl = 'waitingLbl';

//BattleRoomFindOpponentScreen
const bestOfLuckLbl = 'bestOfLuckLbl';
const findingOpponentLbl = 'findingOpponentLbl';
const foundOpponentLbl = 'foundOpponentLbl';
const getReadyLbl = 'getReadyLbl';
const opponentNotFoundLbl = 'opponentNotFoundLbl';
const retryLbl = 'retryLbl';

// battle BattleRoomQuizScreen
const opponentLeftLbl = 'opponentLeftLbl';
const youLeftLbl = 'youLeftLbl';
const youWonLbl = 'youWonLbl';

//MultiUserBattleRoomQuizScreen
const everyOneLeftLbl = 'everyOneLeftLbl';

//MultiUserBattleRoomResultScreen
const exitLbl = 'exitLbl';
const resultLbl = 'resultLbl';
const youLostLbl = 'youLostLbl';

//home QuizTypeContainer
const enterReferralCodeLbl = 'enterReferralCodeLbl';
const referralCodeLbl = 'referralCodeLbl';

//homeScreen
const challengeYourselfLbl = 'challengeYourselfLbl';
const selfChallengeLbl = 'selfChallengeLbl';

// profile ChooseProfileDialog widget
const cameraLbl = 'cameraLbl';
const photoLibraryLbl = 'photoLibraryLbl';

// profile EditProfileFieldBottomSheetContainer
const enterValidEmailMsg = 'enterValidEmailMsg';
const updateLbl = 'updateLbl';
const updatingLbl = 'updatingLbl';

//EditProfileFieldDialog
const enterValidNameMsg = 'enterValidNameMsg';

//ProfileScreen
const aboutUs = 'aboutUs';
const bookmarkLbl = 'bookmarkLbl';
const contactUs = 'contactUs';
const howToPlayLbl = 'howToPlayLbl';
const inviteFriendsLbl = 'inviteFriendsLbl';
const logoutDialogLbl = 'logoutDialogLbl';
const logoutLbl = 'logoutLbl';
const mobileNumberLbl = 'mobileNumberLbl';
const nameLbl = 'nameLbl';
const noBtn = 'noBtn';
const notEditMailLbl = 'notEditMailLbl';
const notEditNumberMsg = 'notEditNumberMsg';
const profileLbl = 'profileLbl';
const rateUsLbl = 'rateUsLbl';
const shareAppLbl = 'shareAppLbl';
const termsAndConditions = 'termsAndConditions';
const yesBtn = 'yesBtn';

//SelectProfilePictureScreen
const continueLbl = 'continueLbl';
const enterNameLbl = 'enterNameLbl';
const selectProfileLbl = 'selectProfileLbl';
const selectProfilePhotoLbl = 'selectProfilePhotoLbl';
const uploadProfilePictureLbl = 'uploadProfilePictureLbl';

//MultipleUserDetailsContainer
const completedLbl = 'completedLbl';
const playingLbl = 'Playing';

//BookmarkQuizScreen
const completeAllQueLbl = 'completeAllQueLbl';
const goBAckLbl = 'goBAckLbl';

//'Contest LeaderBoardScreen
const contestLeaderBoardLbl = 'contestLeaderBoardLbl';

//"ContestScreen
const contestLbl = 'contestLbl';
const endsOnLbl = 'endsOnLbl';
const entryFeesLbl = 'entryFeesLbl';
const leaderboardLbl = 'leaderboardLbl';
const liveLbl = 'liveLbl';
const noPastGameLbl = 'noPastGameLbl';
const pastLbl = 'pastLbl';
const playLbl = 'playLbl';
const playersLbl = 'playersLbl';
const playnowLbl = 'playnowLbl';
const upcomingLbl = 'upcomingLbl';

//FunAndLearnTitleScreen
const questionLbl = 'questionLbl';

/// resultScreen
const anotherOpponentBtn = 'anotherOpponentBtn';
const betterNextLbl = 'betterNextLbl';
const congratulationsLbl = 'congratulationsLbl';
const defeatLbl = 'defeatLbl';
const homeBtn = 'homeBtn';
const looserLbl = 'looserLbl';
const matchDrawLbl = 'matchDrawLbl';
const myScoreLbl = 'myScoreLbl';
const nextLevelBtn = 'nextLevelBtn';
const playAgainBtn = 'playAgainBtn';
const reviewAnsBtn = 'reviewAnsBtn';
const shareScoreBtn = 'shareScoreBtn';
const victoryLbl = 'victoryLbl';
const winnerLbl = 'winnerLbl';
const youLossLbl = 'youLossLbl';

// scored 30% or less
const goodEffort = 'goodEffort';
const keepLearning = 'keepLearning';
// scored between 30% and 50%
const wellDone = 'wellDone';
const makingProgress = 'makingProgress';
// scored between 50% and 70%
const greatJob = 'greatJob';
const closerToMastery = 'closerToMastery';
// scored between 70% and 90%
const excellentWork = 'excellentWork';
const keepGoing = 'keepGoing';
// scored between 90% and 100%
const fantasticJob = 'fantasticJob';
const achievedMastery = 'achievedMastery';

/// reviewAnswerScreen
const correctAndLbl = 'correctAndLbl';
const notesLbl = 'notesLbl';
const reviewAnswerLbl = 'reviewAnswerLbl';
const yourAnsLbl = 'yourAnsLbl';

/// SelectRoomScreen
const enterRoomCodeMsg = 'enterroomerrorMSG';
const oneToOneLbl = 'oneToOneLbl';
const privateRoomLbl = 'privateRoomLbl';
const publicRoomLbl = 'publicRoomLbl';

//SelfChallengeQuestionsScreen
const attemptedLbl = 'attemptedLbl';
const unAttemptedLbl = 'unAttemptedLbl';
const selfChallenge = 'selfChallenge';

//self-challenge Screen
const selectNoQusLbl = 'selectNoQusLbl';
const selectTimeLbl = 'selectTimeLbl';

//SubCategoryAndLevelScreen
const levelLbl = 'levelLbl';

//bookmark Screen
const noBookmarkQueLbl = 'noBookmarkQueLbl';
const playBookmarkBtn = 'playBookmarkBtn';
//CoinStoreScreen
const coinsLbl = 'coinsLbl';
const offerLbl = 'offerLbl';
const removeAdsLbl = 'removeAds';
const storeLbl = 'storeLbl';
//IntroSliderScreen
const description1 = 'description1';
const description2 = 'description2';
const description3 = 'description3';
const title1 = 'title1';
const title2 = 'title2';
const title3 = 'title3';

//LeaderBoardScreen
const dailyLbl = 'dailyLbl';
const monthLbl = 'monthLbl';
const allTimeLbl = 'allTimeLbl';

//ReferAndEarnScreen
const referAndEarn = 'referAndEarn';
const referCodeCopyMsg = 'referCodeCopyMsg';
const referFrdLbl = 'referFrdLbl';
const shareNowLbl = 'shareNowLbl';
const yourRefCOdeLbl = 'yourRefCOdeLbl';

//RewardsScreen
const completeSubTitle = 'completeSubTitle';
const quizFanLbl = 'quizFanLbl';
const rewardsLbl = 'rewardsLbl';

//SplashScreen
const quizLbl = 'quizLbl';
//ExitGameDialog
const quizExitLbl = 'quizExitLbl';
//FontSizeDialog
const fontSizeLbl = 'fontSizeLbl';
//SettingsDialogContainer
const settingLbl = 'settingLbl';
const soundLbl = 'soundLbl';
const vibrationLbl = 'vibrationLbl';

//quizTypes
const audioQuestionsKey = 'audioQuestions';
const battleQuiz = 'battleQuiz';
const contest = 'contest';
const dailyQuiz = 'dailyQuiz';
const desTrueFalseKey = 'desTrueFalse';
const examKey = 'exam';
const funAndLearn = 'funAndLearn';
const groupPlay = 'groupPlay';
const guessTheWord = 'guessTheWord';
const mathManiaKey = 'mathMania';
const quizZone = 'quizZone';
const trueAndFalse = 'trueAndFalse';
const truefalseKey = 'truefalse';

const desAudioQuestionsKey = 'desAudioQuestions';
const desBattleQuiz = 'desBattleQuiz';
const desContest = 'desContest';
const desDailyQuiz = 'desDailyQuiz';
const desExamKey = 'desExam';
const desFunAndLearn = 'desFunAndLearn';
const desGroupPlay = 'desGroupPlay';
const desGuessTheWord = 'desGuessTheWord';
const desMathManiaKey = 'desMathMania';
const desQuizZone = 'desQuizZone';
const desTrueAndFalse = 'desTrueAndFalse';

const noCoinsMsg = 'noCoinsMsg';

const otpNotMatchMsg = 'otpNotMatchMsg';
const rankLbl = 'rankLbl';
const scoreLbl = 'scoreLbl';
const uploadingBtn = 'uploadingBtn';

const cancelButtonKey = 'cancel';
const enterReasonKey = 'enterReason';
const iHaveInviteCodeKey = 'iHaveInviteCode';
const letsStart = 'letsStart';
const notesKey = 'notesLbl';
const reportQuestionKey = 'reportQuestion';
const unableToCreateRoomKey = 'unableToCreateRoom';
const unableToFindRoomKey = 'unableToFindRoom';
const unableToJoinRoomKey = 'unableToJoinRoom';
const unableToSubmitAnswerKey = 'unableToSubmitAnswer';

const aboutQuizAppKey = 'aboutQuizApp';
const accountKey = 'account';
const badgesKey = 'badges';
const chatKey = 'chat';
const coinStoreKey = 'coinStore';
const coinsBoughtSuccessKey = 'coinsBoughtSuccess';
const createRoomKey = 'createRoom';
const currentCoinsKey = 'currentCoins';
const currentlyNotAvailableKey = 'currentlyNotAvailable';
const darkThemeKey = 'darkTheme';
const emojisKey = 'emojis';
const enterRoomCodeHereKey = 'enterRoomCodeHere';
const failedToGetAppUrlKey = 'failedToGetAppUrl';
const inAppPurchaseUnavailableKey = 'inAppPurchaseUnavailable';
const joinRoomKey = 'joinRoom';
const languageKey = 'language';
const lightThemeKey = 'lightTheme';
const systemThemeKey = 'systemTheme';
const messagesKey = 'messages';
const moreThanZeroCoinsKey = 'moreThanZeroCoins';
const noProductsKey = 'noProducts';
const pleaseSelectCategoryKey = 'pleaseSelectCategory';
const productsFetchedFailureKey = 'productsFetchedFailure';
const purchaseErrorKey = 'purchaseError';
const removeAdsBoughtSuccessKey = 'removeAdsBoughtSuccess';
const selectCategoryKey = 'selectCategory';
const selectQuestionsNumberKey = 'selectQuestionsNumber';
const selectSubCategoryKey = 'selectSubCategory';
const showOptionsKey = 'showOptions';
const themeKey = 'theme';
const updateApplicationKey = 'updateApplication';
const updateKey = 'update';
const warningKey = 'warning';

const needMoreKey = 'needMore';
const correctAnswerToUnlockKey = 'correctAnswerToUnlock';

const backKey = 'back';
const byUnlockingKey = 'byUnlocking';
const coinsUnlockingByBadgeKey = 'coinsUnlockingByBadge';
const getKey = 'get';
const hintKey = 'hint';
const noRewardsKey = 'noRewards';
const questionsKey = 'questions';
const scratchHereKey = 'scratchHere';
const totalRewardsEarnedKey = 'totalRewardsEarned'; //

const myRankKey = 'myRank';

const accountDeletedSuccessfullyKey = 'accountDeletedSuccessfully';
const battleStatisticsKey = 'battleStatistics';
const coinHistoryKey = 'coinHistory';
const collectedBadgesKey = 'collectedBadges';
const completedInKey = 'completedIn';
const correctKey = 'correct';
const deletingAccountKey = 'deletingAccount';
const enterExamKey = 'enterExamKey';
const enterValidExamKey = 'enterValidExamKey';
const examDurationKey = 'examDuration';
const examResultKey = 'examResult';
const examRulesKey = 'examRules';
const helloKey = 'hello';
const iAgreeWithExamRulesKey = 'iAgreeWithExamRules';
const incorrectKey = 'incorrect';
const lostKey = 'lost';
const markKey = 'mark';
const minimumRedeemableAmountKey = 'minimumRedeemableAmount';
const notEnoughCoinsToRedeemAmountKey = 'notEnoughCoinsToRedeemAmount';
const obtainedMarksLblKey = 'obtainedMarks';
const playedKey = 'played';
const pleaseAcceptExamRulesKey = 'pleaseAcceptExamRules';
const questionDetailsKey = 'questionDetails';
const quizDetailsKey = 'quizDetails';
const redeemNowKey = 'redeemNow';
const redeemRequestKey = 'redeemRequest';
const redeemableAmountKey = 'redeemableAmount';
const requestKey = 'request';
const statisticsLabelKey = 'statisticsLabel';
const totalCoinsKey = 'totalCoins';
const totalEarningsKey = 'totalEarnings';
const totalKey = 'total';
const totalQuestionsKey = 'totalQuestions';
const transactionKey = 'transaction';
const viewAllKey = 'viewAll';
const viewAllRulesKey = 'viewAllRules';
const walletKey = 'wallet';
const wonKey = 'won';
const youLeftTheExamKey = 'youLeftTheExam';

//coins type
const alreadyLoggedInKey = 'alreadyLoggedIn';
const appUnderMaintenanceKey = 'appUnderMaintenance';
const battleOfTheDayKey = 'battleOfTheDay';
const boughtCoinsKey = 'boughtCoins';
const changePayoutMethodKey = 'changePayoutMethod';
const coinsWillBeDeductedKey = 'coinsWillBeDeducted';
const completedKey = 'completedLbl';
const guestMode = 'guestMode';
const letsPlay = 'letsplay';
const makeRequestKey = 'makeRequest';
const payoutMethodKey = 'payoutMethod';
const pendingKey = 'pending';
const playDifferentZoneKey = 'playDifferentZone';
const playedBattleKey = 'playedBattle';
const playedContestKey = 'playedContest';
const playedGroupBattleKey = 'playedGroupBattle';
const pleaseFillAllDataKey = 'pleaseFillAllData';
const redeemedAmountKey = 'redeemedAmount';
const referredCodeToFriendKey = 'referredCodeToFriend';
const requestingKey = 'requesting';
const rewardByScratchingCardKey = 'rewardByScratchingCard';
const selectPayoutOptionKey = 'selectPayoutOption';
const selfExamZoneKey = 'selfExamZone';
const successfullyRequestedKey = 'successfullyRequested';
const theyWillGetKey = 'theyWillGet';
const trackRequestKey = 'trackRequest';
const used5050lifelineKey = 'used5050lifeline';
const usedAudiencePollLifelineKey = 'usedAudiencePolllifeline';
const usedHintLifelineKey = 'usedHintLifeline';
const usedResetTimerLifelineKey = 'usedResetTimerlifeline';
const usedSkipLifelineKey = 'usedSkiplifeline';
const watchedRewardAdKey = 'watchedRewardAd';
const wonAudioQuizKey = 'wonAudioQuiz';
const wonBattleKey = 'wonBattle';
const wonContestKey = 'wonContest';
const wonDailyQuizKey = 'wonDailyQuiz';
const wonFunNLearnKey = 'wonFunNLearn';
const wonGroupBattleKey = 'wonGroupBattle';
const wonGuessTheWordKey = 'wonGuessTheWord';
const wonMathQuizKey = 'wonMathQuiz';
const wonQuizZoneKey = 'wonQuizZone';
const wonTrueFalseKey = 'wonTrueFalse';
const wrongDetailsKey = 'wrongDetails';
const youWillGetKey = 'youWillGet';

// Voice Chat
const voiceChat = 'voiceChat';
const enableVoiceChat = 'enableVoiceChat';
const enableVoiceChatDesc = 'enableVoiceChatDesc';
const mute = 'mute';
const unmute = 'unmute';
const speakerOn = 'speakerOn';
const speakerOff = 'speakerOff';
const reconnect = 'reconnect';
