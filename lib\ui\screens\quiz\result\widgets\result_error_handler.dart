import 'package:flutter/material.dart';
import 'package:flutterquiz/utils/extensions.dart';

/// ويدجت معالجة الأخطاء المحسن لصفحة النتائج
class ResultErrorHandler extends StatelessWidget {
  final String? errorMessage;
  final VoidCallback? onRetry;
  final VoidCallback? onGoHome;

  const ResultErrorHandler({
    super.key,
    this.errorMessage,
    this.onRetry,
    this.onGoHome,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الخطأ
            _buildErrorIcon(context),
            const SizedBox(height: 24),
            
            // عنوان الخطأ
            _buildErrorTitle(context),
            const SizedBox(height: 16),
            
            // رسالة الخطأ
            _buildErrorMessage(context),
            const SizedBox(height: 32),
            
            // أزرار الإجراءات
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  /// بناء أيقونة الخطأ
  Widget _buildErrorIcon(BuildContext context) {
    return Container(
      width: 100,
      height: 100,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.error.withOpacity(0.1),
        shape: BoxShape.circle,
        border: Border.all(
          color: Theme.of(context).colorScheme.error.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Icon(
        Icons.error_outline_rounded,
        size: 50,
        color: Theme.of(context).colorScheme.error,
      ),
    );
  }

  /// بناء عنوان الخطأ
  Widget _buildErrorTitle(BuildContext context) {
    return Text(
      'حدث خطأ في حساب النتائج',
      style: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// بناء رسالة الخطأ
  Widget _buildErrorMessage(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.error.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.error.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            'نعتذر عن هذا الخطأ. يرجى المحاولة مرة أخرى.',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          if (errorMessage != null && errorMessage!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              'تفاصيل الخطأ: $errorMessage',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // زر إعادة المحاولة
        if (onRetry != null)
          _buildActionButton(
            context: context,
            title: 'إعادة المحاولة',
            icon: Icons.refresh_rounded,
            color: Theme.of(context).primaryColor,
            onTap: onRetry!,
          ),
        
        if (onRetry != null && onGoHome != null)
          const SizedBox(height: 12),
        
        // زر العودة للرئيسية
        if (onGoHome != null)
          _buildActionButton(
            context: context,
            title: 'العودة للرئيسية',
            icon: Icons.home_rounded,
            color: Colors.grey[600]!,
            onTap: onGoHome!,
            isSecondary: true,
          ),
      ],
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    bool isSecondary = false,
  }) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: ElevatedButton.icon(
        onPressed: onTap,
        icon: Icon(
          icon,
          size: 20,
          color: isSecondary 
              ? color 
              : Theme.of(context).colorScheme.onPrimary,
        ),
        label: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isSecondary 
                ? color 
                : Theme.of(context).colorScheme.onPrimary,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: isSecondary 
              ? Colors.transparent 
              : color,
          foregroundColor: isSecondary 
              ? color 
              : Theme.of(context).colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: isSecondary 
                ? BorderSide(color: color, width: 2)
                : BorderSide.none,
          ),
          elevation: isSecondary ? 0 : 2,
        ),
      ),
    );
  }
}

/// ويدجت حالة التحميل المحسن
class ResultLoadingWidget extends StatelessWidget {
  final String? message;
  final double? progress;

  const ResultLoadingWidget({
    super.key,
    this.message,
    this.progress,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // مؤشر التحميل
            _buildLoadingIndicator(context),
            const SizedBox(height: 24),
            
            // رسالة التحميل
            _buildLoadingMessage(context),
            
            // شريط التقدم (اختياري)
            if (progress != null) ...[
              const SizedBox(height: 16),
              _buildProgressBar(context),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء مؤشر التحميل
  Widget _buildLoadingIndicator(BuildContext context) {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: SizedBox(
          width: 40,
          height: 40,
          child: CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  /// بناء رسالة التحميل
  Widget _buildLoadingMessage(BuildContext context) {
    return Column(
      children: [
        Text(
          message ?? 'جاري حساب النتائج...',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'يرجى الانتظار قليلاً',
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء شريط التقدم
  Widget _buildProgressBar(BuildContext context) {
    return Column(
      children: [
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Theme.of(context).primaryColor.withOpacity(0.2),
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${((progress ?? 0) * 100).toInt()}%',
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }
}
