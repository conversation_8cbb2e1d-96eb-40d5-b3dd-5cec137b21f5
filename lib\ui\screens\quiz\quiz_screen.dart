import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/ads/rewarded_ad_cubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/cubits/questionsCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/subCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/unlockedLevelCubit.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/features/quiz/models/comprehension.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/quiz/quizRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/audioQuestionContainer.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/explanation_dialog.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/customAppbar.dart';
import 'package:flutterquiz/ui/widgets/customRoundedButton.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/exitGameDialog.dart';
import 'package:flutterquiz/ui/widgets/questionsContainer.dart';
import 'package:flutterquiz/ui/widgets/quick_settings_dialog.dart';
import 'package:flutterquiz/ui/widgets/text_circular_timer.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:flutter/services.dart';
// import 'package:video_player/video_player.dart';
// import 'package:chewie/chewie.dart';

enum LifelineStatus { unused, using, used }

class QuizScreen extends StatefulWidget {
  const QuizScreen({
    required this.isPlayed,
    required this.numberOfPlayer,
    required this.subcategoryMaxLevel,
    required this.quizType,
    required this.categoryId,
    required this.level,
    required this.subcategoryId,
    required this.unlockedLevel,
    required this.contestId,
    required this.comprehension,
    required this.isPremiumCategory,
    super.key,
    this.showRetryButton = true,
  });

  final int numberOfPlayer;
  final QuizTypes quizType;
  final String level; //will be in use for quizZone quizType
  final String categoryId; //will be in use for quizZone quizType
  final String subcategoryId; //will be in use for quizZone quizType
  final String
      subcategoryMaxLevel; //will be in use for quizZone quizType (to pass in result screen)
  final int unlockedLevel;
  final bool isPlayed; //Only in use when quiz type is audio questions
  final String contestId;
  final Comprehension
      comprehension; // will be in use for fun n learn quizType (to pass in result screen)

  // only used for when there is no questions for that category,
  // and showing retry button doesn't make any sense i guess.
  final bool showRetryButton;
  final bool isPremiumCategory;

  @override
  State<QuizScreen> createState() => _QuizScreenState();

  //to provider route
  static Route<dynamic> route(RouteSettings routeSettings) {
    final arguments = routeSettings.arguments! as Map;
    //keys of arguments are numberOfPlayer and quizType (required)
    //if quizType is quizZone then need to pass following keys
    //categoryId, subcategoryId, level, subcategoryMaxLevel and unlockedLevel

    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          //for questions and points
          BlocProvider<QuestionsCubit>(
            create: (_) => QuestionsCubit(QuizRepository()),
          ),
          //to update user coins after using lifeline
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
        ],
        child: QuizScreen(
          isPlayed: arguments['isPlayed'] as bool? ?? true,
          numberOfPlayer: arguments['numberOfPlayer'] as int,
          quizType: arguments['quizType'] as QuizTypes,
          categoryId: arguments['categoryId'] as String? ?? '',
          level: arguments['level'] as String? ?? '',
          subcategoryId: arguments['subcategoryId'] as String? ?? '',
          subcategoryMaxLevel:
              arguments['subcategoryMaxLevel'] as String? ?? '',
          unlockedLevel: arguments['unlockedLevel'] as int? ?? 0,
          contestId: arguments['contestId'] as String? ?? '',
          comprehension: arguments['comprehension'] as Comprehension? ??
              Comprehension.empty(),
          showRetryButton: arguments['showRetryButton'] as bool? ?? true,
          isPremiumCategory: arguments['isPremiumCategory'] as bool? ?? false,
        ),
      ),
    );
  }
}

class _QuizScreenState extends State<QuizScreen> with TickerProviderStateMixin {
  // إضافة متغير جديد لتتبع استخدام المساعدة في السؤال الحالي
  Map<String, bool> questionsUsingHelp = {};

  late AnimationController questionAnimationController;
  late AnimationController questionContentAnimationController;
  late AnimationController audioTimerController = AnimationController(
    vsync: this,
    duration: Duration(
      seconds: widget.quizType == QuizTypes.audioQuestions
          ? context
              .read<SystemConfigCubit>()
              .quizTimer(QuizTypes.audioQuestions)
          : 0,
    ),
  );
  late final timerAnimationController = AnimationController(
    vsync: this,
    reverseDuration: const Duration(seconds: inBetweenQuestionTimeInSeconds),
    duration: Duration(
      seconds: context.read<SystemConfigCubit>().quizTimer(widget.quizType),
    ),
  )..addStatusListener(currentUserTimerAnimationStatusListener);

  late Animation<double> questionSlideAnimation;
  late Animation<double> questionScaleUpAnimation;
  late Animation<double> questionScaleDownAnimation;
  late Animation<double> questionContentAnimation;
  late AnimationController animationController;
  late AnimationController topContainerAnimationController;
  late AnimationController showOptionAnimationController = AnimationController(
    vsync: this,
    duration: const Duration(milliseconds: 500),
  );
  late Animation<double> showOptionAnimation =
      Tween<double>(begin: 0, end: 1).animate(
    CurvedAnimation(
      parent: showOptionAnimationController,
      curve: Curves.easeInOut,
    ),
  );
  late List<GlobalKey<AudioQuestionContainerState>> audioQuestionContainerKeys =
      [];
  int currentQuestionIndex = 0;
  final double optionWidth = 0.7;
  final double optionHeight = 0.09;

  late double totalSecondsToCompleteQuiz = 0;

  late Map<String, LifelineStatus> lifelines = {
    fiftyFifty: LifelineStatus.unused,
    audiencePoll: LifelineStatus.unused,
    skip: LifelineStatus.unused,
    resetTime: LifelineStatus.unused,
  };

  //to track if setting dialog is open
  bool isSettingDialogOpen = false;
  bool isExitDialogOpen = false;

  // VideoPlayerController? _videoController;
  // ChewieController? _chewieController;
  void _getQuestions() {
    Future.delayed(
      Duration.zero,
      () {
        context.read<QuestionsCubit>().getQuestions(
              widget.quizType,
              categoryId: widget.categoryId,
              level: widget.level,
              languageId: UiUtils.getCurrentQuizLanguageId(context),
              subcategoryId: widget.subcategoryId,
              contestId: widget.contestId,
              funAndLearnId: widget.comprehension.id,
            );

        // ✅ إنشاء Rewarded Ad للاستخدام قبل النتائج
        context.read<RewardedAdCubit>().createRewardedAd(context);
      },
    );
  }

  @override
  void initState() {
    super.initState();

    //init animations
    initializeAnimation();
    animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    topContainerAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    //
    _getQuestions();
  }

  void initializeAnimation() {
    questionContentAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250),
    );
    questionAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 525),
    );
    questionSlideAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: Curves.easeInOut,
      ),
    );
    questionScaleUpAnimation = Tween<double>(begin: 0, end: 0.1).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0, 0.5, curve: Curves.easeInQuad),
      ),
    );
    questionContentAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: questionContentAnimationController,
        curve: Curves.easeInQuad,
      ),
    );
    questionScaleDownAnimation = Tween<double>(begin: 0, end: 0.05).animate(
      CurvedAnimation(
        parent: questionAnimationController,
        curve: const Interval(0.5, 1, curve: Curves.easeOutQuad),
      ),
    );
  }

  @override
  void dispose() {
    timerAnimationController
      ..removeStatusListener(currentUserTimerAnimationStatusListener)
      ..dispose();
    questionAnimationController.dispose();
    questionContentAnimationController.dispose();
    // _disposeVideoControllers();
    super.dispose();
  }

  // void _disposeVideoControllers() {
  //   _videoController?.dispose();
  //   _chewieController?.dispose();
  //   _videoController = null;
  //   _chewieController = null;
  // }

  void toggleSettingDialog() {
    isSettingDialogOpen = !isSettingDialogOpen;
  }

  void navigateToResultScreen() {
    if (isSettingDialogOpen) {
      Navigator.of(context).pop();
    }
    if (isExitDialogOpen) {
      Navigator.of(context).pop();
    }

    // ✅ الانتقال مباشرة إلى صفحة النتائج أولاً
    _navigateToResult();
  }

  void _navigateToResult() {
    //move to result page
    //to see the what are the keys to pass in arguments for result screen
    //visit static route function in resultScreen.dart
    Navigator.of(context).pushReplacementNamed(
      Routes.result,
      arguments: {
        'numberOfPlayer': widget.numberOfPlayer,
        'myPoints': context.read<QuestionsCubit>().currentPoints(),
        'quizType': widget.quizType,
        'questions': context.read<QuestionsCubit>().questions(),
        'subcategoryMaxLevel': widget.subcategoryMaxLevel,
        'unlockedLevel': widget.unlockedLevel,
        'categoryId': widget.categoryId,
        'subcategoryId': widget.subcategoryId,
        'contestId': widget.contestId,
        'isPlayed': widget.isPlayed,
        'comprehension': widget.comprehension,
        'timeTakenToCompleteQuiz': totalSecondsToCompleteQuiz,
        'hasUsedAnyLifeline': checkHasUsedAnyLifeline(),
        'entryFee': 0,
        'isPremiumCategory': widget.isPremiumCategory,
        'questionsUsingHelp': questionsUsingHelp,
        'numberOfQuestionsWithHelp':
            questionsUsingHelp.values.where((used) => used).length,
      },
    );
  }

  void markLifeLineUsed() {
    if (lifelines[fiftyFifty] == LifelineStatus.using) {
      lifelines[fiftyFifty] = LifelineStatus.used;
    }
    if (lifelines[audiencePoll] == LifelineStatus.using) {
      lifelines[audiencePoll] = LifelineStatus.used;
    }
    if (lifelines[resetTime] == LifelineStatus.using) {
      lifelines[resetTime] = LifelineStatus.used;
    }
    if (lifelines[skip] == LifelineStatus.using) {
      lifelines[skip] = LifelineStatus.used;
    }
  }

  bool checkHasUsedAnyLifeline() {
    var hasUsedAnyLifeline = false;

    for (final lifelineStatus in lifelines.values) {
      if (lifelineStatus == LifelineStatus.used) {
        hasUsedAnyLifeline = true;
        break;
      }
    }
    //
    return hasUsedAnyLifeline;
  }

  //change to next Question

  void changeQuestion() {
    questionAnimationController.forward(from: 0).then((value) {
      //need to dispose the animation controllers
      questionAnimationController.dispose();
      questionContentAnimationController.dispose();
      //initializeAnimation again
      setState(() {
        initializeAnimation();
        currentQuestionIndex++;
        markLifeLineUsed();
      });
      //load content(options, image etc) of question
      questionContentAnimationController.forward();
    });
  }

  //if user has submitted the answer for current question
  bool hasSubmittedAnswerForCurrentQuestion() {
    return context
        .read<QuestionsCubit>()
        .questions()[currentQuestionIndex]
        .attempted;
  }

  Map<String, LifelineStatus> getLifeLines() {
    if (widget.quizType == QuizTypes.quizZone ||
        widget.quizType == QuizTypes.dailyQuiz) {
      return lifelines;
    }
    return {};
  }

  void updateTotalSecondsToCompleteQuiz() {
    final configCubit = context.read<SystemConfigCubit>();
    totalSecondsToCompleteQuiz = totalSecondsToCompleteQuiz +
        UiUtils.timeTakenToSubmitAnswer(
          animationControllerValue: timerAnimationController.value,
          quizTimer: configCubit.quizTimer(widget.quizType),
        );
  }

  //update answer locally and on cloud
  Future<void> submitAnswer(String submittedAnswer) async {
    timerAnimationController.stop(canceled: false);
    if (!context
        .read<QuestionsCubit>()
        .questions()[currentQuestionIndex]
        .attempted) {
      final questionId =
          context.read<QuestionsCubit>().questions()[currentQuestionIndex].id;
      final hasUsedHelp = questionsUsingHelp[questionId] ?? false;

      // تعديل النقاط بناءً على استخدام المساعدة
      final correctScore = hasUsedHelp
          ? 0
          : context
              .read<SystemConfigCubit>()
              .quizCorrectAnswerCreditScore(widget.quizType);
      final wrongScore = hasUsedHelp
          ? 0
          : context
              .read<SystemConfigCubit>()
              .quizWrongAnswerDeductScore(widget.quizType);

      context.read<QuestionsCubit>().updateQuestionWithAnswerAndLifeline(
            questionId,
            submittedAnswer,
            context.read<UserDetailsCubit>().getUserFirebaseId(),
            correctScore,
            wrongScore,
          );
      updateTotalSecondsToCompleteQuiz();
      await timerAnimationController.reverse();
      //change question
      await Future<void>.delayed(
        const Duration(seconds: inBetweenQuestionTimeInSeconds),
      );

      if (currentQuestionIndex !=
          (context.read<QuestionsCubit>().questions().length - 1)) {
        changeQuestion();
        //if quizType is latex(math or chemistry) then start timer again
        // تم إزالة QuizTypes.audioQuestions لأن الأسئلة الصوتية لم تعد مدعومة
        // تم إزالة MathMania لأن اختبارات الرياضيات لم تعد مدعومة
        // if (widget.quizType == QuizTypes.mathMania) {
        //   timerAnimationController.value = 0.0;
        //   await showOptionAnimationController.forward();
        // } else {
        {
          await timerAnimationController.forward(from: 0);
        }
      } else {
        navigateToResultScreen();
      }
    }
  }

  //listener for current user timer
  void currentUserTimerAnimationStatusListener(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      submitAnswer('-1');
    } else if (status == AnimationStatus.forward) {
      // تم إزالة الشرط المتعلق بـ AudioQuestions لأن الأسئلة الصوتية لم تعد مدعومة
    }
  }

  Widget _buildShowOptionButton() {
    if (widget.quizType == QuizTypes.audioQuestions) {
      return Align(
        alignment: Alignment.bottomCenter,
        child: SlideTransition(
          position: showOptionAnimation.drive<Offset>(
            Tween<Offset>(begin: const Offset(0, 1.5), end: Offset.zero),
          ),
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).size.height * (0.025),
              left: MediaQuery.of(context).size.width * UiUtils.hzMarginPct,
              right: MediaQuery.of(context).size.width * UiUtils.hzMarginPct,
            ),
            child: CustomRoundedButton(
              widthPercentage: MediaQuery.of(context).size.width,
              backgroundColor: Theme.of(context).primaryColor,
              buttonTitle: context.tr(showOptionsKey),
              titleColor: Theme.of(context).colorScheme.surface,
              onTap: () {
                if (!showOptionAnimationController.isAnimating) {
                  showOptionAnimationController.reverse();
                  audioQuestionContainerKeys[currentQuestionIndex]
                      .currentState!
                      .changeShowOption();
                  timerAnimationController.forward(from: 0);
                }
              },
              showBorder: false,
              radius: 8,
              height: 40,
              elevation: 5,
              fontWeight: FontWeight.w600,
              textSize: 18,
            ),
          ),
        ),
      );
    }
    return const SizedBox();
  }

  void onTapBackButton() {
    isExitDialogOpen = true;
    showDialog<void>(
      context: context,
      builder: (_) => ExitGameDialog(
        onTapYes: (widget.quizType == QuizTypes.quizZone)
            ? () {
                Navigator.of(context).pop(true);
                Navigator.of(context).pop(true);
              }
            : null,
      ),
    ).then(
      (_) {
        if (!mounted) return; // التحقق من mounted

        if (widget.quizType == QuizTypes.quizZone) {
          try {
            if (widget.subcategoryId == '0' || widget.subcategoryId == '') {
              // التحقق من وجود context
              if (context.mounted) {
                context.read<UnlockedLevelCubit>().fetchUnlockLevel(
                      widget.categoryId,
                      '0',
                    );
              }
            } else {
              // التحقق من وجود context
              if (context.mounted) {
                context.read<SubCategoryCubit>().fetchSubCategory(
                      widget.categoryId,
                    );
              }
            }
          } catch (e) {
            print("Error updating level: $e");
          }
        }
      },
    );
  }

  bool get isSmallDevice => MediaQuery.sizeOf(context).width <= 360;

  Duration get timer =>
      timerAnimationController.duration! -
      timerAnimationController.lastElapsedDuration!;

  String get remaining => (timerAnimationController.isAnimating)
      ? "${timer.inMinutes.remainder(60).toString().padLeft(2, '0')}:${timer.inSeconds.remainder(60).toString().padLeft(2, '0')}"
      : '';

  @override
  Widget build(BuildContext context) {
    final quesCubit = context.read<QuestionsCubit>();

    return BlocListener<UpdateScoreAndCoinsCubit, UpdateScoreAndCoinsState>(
      listener: (context, state) {
        if (state is UpdateScoreAndCoinsFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            timerAnimationController.stop();
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      child: BlocConsumer<QuestionsCubit, QuestionsState>(
        bloc: quesCubit,
        listener: (_, state) {
          if (state is QuestionsFetchSuccess) {
            if (state.questions.isNotEmpty) {
              if (currentQuestionIndex == 0 &&
                  !state.questions[currentQuestionIndex].attempted) {
                if (widget.quizType == QuizTypes.audioQuestions) {
                  for (final _ in state.questions) {
                    audioQuestionContainerKeys.add(
                      GlobalKey<AudioQuestionContainerState>(),
                    );
                  }

                  //
                  showOptionAnimationController.forward();
                  questionContentAnimationController.forward();
                  //add audio question container keys
                }

                //
                // تم إزالة MathMania لأن اختبارات الرياضيات لم تعد مدعومة
                // else if (widget.quizType == QuizTypes.mathMania) {
                //   questionContentAnimationController.forward();
                // } else {
                else {
                  timerAnimationController.forward();
                  questionContentAnimationController.forward();
                }
              }
            }
          } else if (state is QuestionsFetchFailure) {
            if (state.errorMessage == errorCodeUnauthorizedAccess) {
              showAlreadyLoggedInDialog(context);
            }
          }
        },
        builder: (context, state) {
          if (state is QuestionsFetchInProgress || state is QuestionsIntial) {
            return const Scaffold(
              body: Center(child: CircularProgressContainer()),
            );
          }
          if (state is QuestionsFetchFailure) {
            return Scaffold(
              appBar: const QAppBar(title: SizedBox(), roundedAppBar: false),
              body: Center(
                child: ErrorContainer(
                  showBackButton: true,
                  errorMessage:
                      convertErrorCodeToLanguageKey(state.errorMessage),
                  showRTryButton: widget.showRetryButton &&
                      convertErrorCodeToLanguageKey(state.errorMessage) !=
                          dailyQuizAlreadyPlayedKey,
                  onTapRetry: _getQuestions,
                  showErrorImage: true,
                ),
              ),
            );
          }

          return PopScope(
            canPop: false,
            onPopInvokedWithResult: (didPop, _) {
              if (didPop) return;
              onTapBackButton();
            },
            child: Scaffold(
              body: Stack(
                children: [
                  // خلفية الصفحة بتصميم محسن ومتدرج جميل
                  Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: Theme.of(context).brightness == Brightness.dark
                            ? [
                                Theme.of(context).primaryColor.withOpacity(0.4),
                                Theme.of(context).primaryColor.withOpacity(0.2),
                                Theme.of(context)
                                    .colorScheme
                                    .surface
                                    .withOpacity(0.9),
                                Theme.of(context).colorScheme.surface,
                              ]
                            : [
                                Theme.of(context).primaryColor.withOpacity(0.9),
                                Theme.of(context).primaryColor.withOpacity(0.6),
                                Theme.of(context)
                                    .colorScheme
                                    .surface
                                    .withOpacity(0.95),
                                Theme.of(context).colorScheme.surface,
                              ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        stops: const [0.0, 0.3, 0.7, 1.0],
                      ),
                    ),
                  ),

                  // زخارف الخلفية المحسنة
                  Positioned(
                    top: -80,
                    right: -80,
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors:
                              Theme.of(context).brightness == Brightness.dark
                                  ? [
                                      Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.15),
                                      Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.05),
                                      Colors.transparent,
                                    ]
                                  : [
                                      Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.3),
                                      Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.1),
                                      Colors.transparent,
                                    ],
                        ),
                      ),
                    ),
                  ),

                  Positioned(
                    bottom: -100,
                    left: -100,
                    child: Container(
                      width: 250,
                      height: 250,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors:
                              Theme.of(context).brightness == Brightness.dark
                                  ? [
                                      Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.12),
                                      Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.04),
                                      Colors.transparent,
                                    ]
                                  : [
                                      Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.25),
                                      Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.08),
                                      Colors.transparent,
                                    ],
                        ),
                      ),
                    ),
                  ),

                  // زخارف إضافية للجمالية
                  Positioned(
                    top: MediaQuery.of(context).size.height * 0.3,
                    right: -30,
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).primaryColor.withOpacity(
                            Theme.of(context).brightness == Brightness.dark
                                ? 0.05
                                : 0.1),
                      ),
                    ),
                  ),

                  Positioned(
                    top: MediaQuery.of(context).size.height * 0.6,
                    left: -20,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).primaryColor.withOpacity(
                            Theme.of(context).brightness == Brightness.dark
                                ? 0.06
                                : 0.12),
                      ),
                    ),
                  ),

                  // شريط العنوان المحسن والجذاب
                  Positioned(
                    top: MediaQuery.of(context).padding.top + 15,
                    left: 20,
                    right: 20,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 8),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .surface
                            .withOpacity(0.95),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context)
                                .primaryColor
                                .withOpacity(0.15),
                            blurRadius: 20,
                            offset: const Offset(0, 5),
                            spreadRadius: 2,
                          ),
                        ],
                        border: Border.all(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // زر الرجوع بتصميم محسن
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Theme.of(context).primaryColor,
                                  Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.8),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(18),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.arrow_back_ios_new_rounded,
                                color: Theme.of(context).colorScheme.onPrimary,
                                size: 18,
                              ),
                              onPressed: onTapBackButton,
                            ),
                          ),

                          // Settings Button
                          Container(
                            decoration: BoxDecoration(
                              color: Theme.of(context)
                                  .primaryColor
                                  .withOpacity(0.1),
                              borderRadius: BorderRadius.circular(18),
                            ),
                            child: IconButton(
                              icon: Icon(
                                Icons.settings_rounded,
                                color: Theme.of(context).primaryColor,
                                size: 22,
                              ),
                              onPressed: () {
                                showQuickSettingsDialog(context);
                              },
                            ),
                          ),

                          // Timer Container - تصميم محسن ومتطور للتايمر
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Theme.of(context).colorScheme.surface,
                                  Theme.of(context)
                                      .colorScheme
                                      .surface
                                      .withOpacity(0.9),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.2),
                                  blurRadius: 15,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 1,
                                ),
                              ],
                              border: Border.all(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.1),
                                width: 1,
                              ),
                            ),
                            padding: const EdgeInsets.all(6),
                            child: widget.quizType == QuizTypes.funAndLearn
                                ? Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 18,
                                      vertical: 10,
                                    ),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Theme.of(context).primaryColor,
                                          Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.8),
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      borderRadius: BorderRadius.circular(16),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.3),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.timer_outlined,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onPrimary,
                                          size: 18,
                                        ),
                                        const SizedBox(width: 6),
                                        AnimatedBuilder(
                                          animation: timerAnimationController,
                                          builder: (context, _) => Text(
                                            remaining,
                                            style: TextStyle(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onPrimary,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : Container(
                                    padding: const EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      gradient: LinearGradient(
                                        colors: [
                                          Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.1),
                                          Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.05),
                                        ],
                                      ),
                                    ),
                                    child: TextCircularTimer(
                                      animationController:
                                          timerAnimationController,
                                      arcColor: Theme.of(context).primaryColor,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withOpacity(0.2),
                                      size: 52.0,
                                      strokeWidth: 5.0,
                                    ),
                                  ),
                          ),

                          // Question Counter - عداد الأسئلة المحسن
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 18.0, vertical: 10.0),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Theme.of(context).colorScheme.surface,
                                  Theme.of(context)
                                      .colorScheme
                                      .surface
                                      .withOpacity(0.95),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.2),
                                  blurRadius: 15,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 1,
                                ),
                              ],
                              border: Border.all(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.1),
                                width: 1,
                              ),
                            ),
                            child: BlocBuilder<QuestionsCubit, QuestionsState>(
                              builder: (context, state) {
                                if (state is QuestionsFetchSuccess) {
                                  return Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(6),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.1),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.quiz_outlined,
                                          color: Theme.of(context).primaryColor,
                                          size: 16,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        "${currentQuestionIndex + 1}/${state.questions.length}",
                                        style: TextStyle(
                                          color: Theme.of(context).primaryColor,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // محتوى الأسئلة المحسن
                  Padding(
                    padding: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top + 80,
                      left: 16,
                      right: 16,
                      bottom: 80,
                    ),
                    child: Card(
                      elevation: 12,
                      shadowColor:
                          Theme.of(context).primaryColor.withOpacity(0.2),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Theme.of(context).colorScheme.surface,
                              Theme.of(context).colorScheme.surface,
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(25),
                          border: Border.all(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.1),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Expanded(
                              child: QuestionsContainer(
                                quizType: widget.quizType,
                                answerMode: context
                                    .read<SystemConfigCubit>()
                                    .answerMode,
                                lifeLines: getLifeLines(),
                                timerAnimationController:
                                    timerAnimationController,
                                topPadding: 20,
                                hasSubmittedAnswerForCurrentQuestion:
                                    hasSubmittedAnswerForCurrentQuestion,
                                questions:
                                    context.read<QuestionsCubit>().questions(),
                                submitAnswer: submitAnswer,
                                questionContentAnimation:
                                    questionContentAnimation,
                                questionScaleDownAnimation:
                                    questionScaleDownAnimation,
                                questionScaleUpAnimation:
                                    questionScaleUpAnimation,
                                questionSlideAnimation: questionSlideAnimation,
                                currentQuestionIndex: currentQuestionIndex,
                                questionAnimationController:
                                    questionAnimationController,
                                questionContentAnimationController:
                                    questionContentAnimationController,
                                level: widget.level,
                              ),
                            ),

                            // زر "اشرح لي" لعرض ملاحظات السؤال
                            BlocBuilder<QuestionsCubit, QuestionsState>(
                              builder: (context, state) {
                                if (state is QuestionsFetchSuccess) {
                                  final currentQuestion =
                                      state.questions[currentQuestionIndex];

                                  // إضافة logs لتتبع الفيديوهات

                                  final hasNote =
                                      currentQuestion.note != null &&
                                          currentQuestion.note!.isNotEmpty;
                                  final hasVideo = currentQuestion.hasVideo;
                                  final hasExplanation = hasNote || hasVideo;

                                  // تحقق من حجم الشاشة
                                  final screenWidth =
                                      MediaQuery.of(context).size.width;
                                  final isSmallScreen = screenWidth < 400;

                                  return Container(
                                    margin: const EdgeInsets.symmetric(
                                      horizontal: 16.0,
                                      vertical: 8.0,
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 16.0,
                                      horizontal: 12.0,
                                    ),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.05),
                                          Theme.of(context)
                                              .primaryColor
                                              .withOpacity(0.02),
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: Theme.of(context)
                                            .primaryColor
                                            .withOpacity(0.1),
                                        width: 1,
                                      ),
                                    ),
                                    child: Column(
                                      children: [
                                        // العنوان المحسن
                                        Container(
                                          padding:
                                              const EdgeInsets.only(bottom: 12),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.support_agent_outlined,
                                                color: Theme.of(context)
                                                    .primaryColor,
                                                size: 20,
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                "أدوات المساعدة",
                                                style: TextStyle(
                                                  color: Theme.of(context)
                                                      .primaryColor,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),

                                        // استخدام Wrap بدلاً من Row لتجنب تجاوز الحدود
                                        Wrap(
                                          alignment: WrapAlignment.center,
                                          spacing:
                                              10, // المسافة الأفقية بين العناصر
                                          runSpacing:
                                              10, // المسافة الرأسية بين الصفوف
                                          children: [
                                            if (hasExplanation)
                                              _buildHelpButton(
                                                icon: Icons.lightbulb_outline,
                                                title: "اشرح لي",
                                                color: Theme.of(context)
                                                    .primaryColor,
                                                onTap: () =>
                                                    _showExplanationDialog(
                                                        context,
                                                        currentQuestion),
                                                isSmallScreen: isSmallScreen,
                                              ),
                                            _buildHelpButton(
                                              icon: Icons.remove_circle_outline,
                                              title: "احذف إجابتين",
                                              color: Colors.orange,
                                              onTap: _eliminateWrongAnswers,
                                              isSmallScreen: isSmallScreen,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  );
                                }
                                return const SizedBox();
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // أزرار التنقل المحسنة بين الأسئلة
                  Positioned(
                    bottom: 20,
                    left: 20,
                    right: 20,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // زر السؤال السابق المحسن
                        if (currentQuestionIndex > 0)
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Theme.of(context).primaryColor,
                                  Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.8),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.4),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                  spreadRadius: 1,
                                ),
                              ],
                              border: Border.all(
                                color: Theme.of(context)
                                    .colorScheme
                                    .onPrimary
                                    .withOpacity(0.2),
                                width: 1,
                              ),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(20),
                                onTap: () {
                                  // التنقل للسؤال السابق
                                  if (currentQuestionIndex > 0) {
                                    setState(() {
                                      currentQuestionIndex--;
                                    });
                                  }
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(12),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onPrimary
                                              .withOpacity(0.2),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.arrow_back_ios_new_rounded,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onPrimary,
                                          size: 18,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        'السابق',
                                        style: TextStyle(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onPrimary,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                        else
                          const SizedBox(width: 80),

                        // زر السؤال التالي المحسن
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Theme.of(context).primaryColor,
                                Theme.of(context).primaryColor.withOpacity(0.8),
                              ],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(20),
                            boxShadow: [
                              BoxShadow(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.4),
                                blurRadius: 12,
                                offset: const Offset(0, 4),
                                spreadRadius: 1,
                              ),
                            ],
                            border: Border.all(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onPrimary
                                  .withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(20),
                              onTap: () {
                                // التنقل للسؤال التالي أو إنهاء الاختبار
                                if (currentQuestionIndex ==
                                    (context
                                            .read<QuestionsCubit>()
                                            .questions()
                                            .length -
                                        1)) {
                                  navigateToResultScreen();
                                } else {
                                  changeQuestion();
                                }
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      currentQuestionIndex ==
                                              (context
                                                      .read<QuestionsCubit>()
                                                      .questions()
                                                      .length -
                                                  1)
                                          ? 'إنهاء'
                                          : 'التالي',
                                      style: TextStyle(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onPrimary,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onPrimary
                                            .withOpacity(0.2),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        currentQuestionIndex ==
                                                (context
                                                        .read<QuestionsCubit>()
                                                        .questions()
                                                        .length -
                                                    1)
                                            ? Icons.check_circle_outline_rounded
                                            : Icons.arrow_forward_ios_rounded,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onPrimary,
                                        size: 18,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // زر إظهار الخيارات
                  _buildShowOptionButton(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // دالة محسنة لإنشاء أزرار المساعدة بتصميم جميل وموحد
  Widget _buildHelpButton({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
    bool isLoading = false,
    bool isWide = false,
    bool isSmallScreen = false,
  }) {
    return Container(
      margin: EdgeInsets.zero,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color,
            color.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.4),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 1,
          ),
        ],
        border: Border.all(
          color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.symmetric(
                vertical: 12,
                horizontal:
                    isSmallScreen ? (isWide ? 16 : 12) : (isWide ? 24 : 18)),
            child: isLoading
                ? SizedBox(
                    width: isSmallScreen ? 16 : 18,
                    height: isSmallScreen ? 16 : 18,
                    child: CircularProgressIndicator(
                      color: Theme.of(context).colorScheme.onPrimary,
                      strokeWidth: isSmallScreen ? 1.5 : 2,
                    ),
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .onPrimary
                              .withOpacity(0.25),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context)
                                  .colorScheme
                                  .shadow
                                  .withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          icon,
                          color: Theme.of(context).colorScheme.onPrimary,
                          size: isSmallScreen ? 16 : 18,
                        ),
                      ),
                      SizedBox(width: isSmallScreen ? 6 : 10),
                      Text(
                        title,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                          fontSize: isSmallScreen ? 12 : 14,
                          shadows: [
                            Shadow(
                              color: Theme.of(context)
                                  .colorScheme
                                  .shadow
                                  .withOpacity(0.2),
                              offset: const Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  void _showExplanationDialog(BuildContext context, Question question) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ExplanationDialog(
          question: question,
          onDialogOpened: () {
            // إيقاف المؤقت عند فتح نافذة الشرح
            timerAnimationController.stop();
          },
          onDialogClosed: () {
            // استكمال المؤقت عند إغلاق نافذة الشرح
            if (mounted && !hasSubmittedAnswerForCurrentQuestion()) {
              timerAnimationController.forward();
            }
          },
        );
      },
    );

    // تحديث حالة استخدام المساعدة للسؤال الحالي
    final questionId = question.id ?? '';
    if (questionId.isNotEmpty) {
      questionsUsingHelp[questionId] = true;
    }
  }

  void _eliminateWrongAnswers() {
    final currentQuestion =
        context.read<QuestionsCubit>().questions()[currentQuestionIndex];

    // التحقق من الشروط الأساسية
    if (currentQuestion.answerOptions == null ||
        currentQuestion.answerOptions!.length <= 2 ||
        currentQuestion.attempted ||
        currentQuestion.correctAnswer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('لا يمكن استخدام هذه الميزة الآن'),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
      return;
    }

    try {
      // نسخ قائمة الخيارات الحالية
      final allOptions =
          List<AnswerOption>.from(currentQuestion.answerOptions!);

      // الحصول على الإجابة الصحيحة
      final correctAnswerId = AnswerEncryption.decryptCorrectAnswer(
        correctAnswer: currentQuestion.correctAnswer!,
        rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
      );

      // التأكد من وجود الإجابة الصحيحة في القائمة
      final correctOption = allOptions.firstWhere(
        (option) => option.id == correctAnswerId,
      );

      // تجميع الإجابات الخاطئة فقط
      final wrongAnswers = allOptions
          .where((option) => option.id != correctAnswerId)
          .toList()
        ..shuffle();

      // التحقق من وجود إجابات خاطئة كافية
      if (wrongAnswers.isEmpty) {
        throw Exception('لا توجد إجابات خاطئة كافية');
      }

      // إنشاء قائمة الإجابات النهائية وترتيبها عشوائياً
      final remainingAnswers = [
        correctOption,
        wrongAnswers.first,
      ]..shuffle(); // هنا نقوم بترتيب القائمة عشوائياً

      // تسجيل استخدام المساعدة
      final questionId = currentQuestion.id ?? '';
      if (questionId.isNotEmpty) {
        questionsUsingHelp[questionId] = true;
      }

      // تحديث السؤال
      setState(() {
        currentQuestion.answerOptions?.clear();
        currentQuestion.answerOptions?.addAll(remainingAnswers);
        lifelines[fiftyFifty] = LifelineStatus.used;
      });

      // عرض رسالة نجاح
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم حذف إجابتين خاطئتين'),
          backgroundColor: Theme.of(context).primaryColor,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('حدث خطأ أثناء محاولة حذف الإجابات'),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(8),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }
}
