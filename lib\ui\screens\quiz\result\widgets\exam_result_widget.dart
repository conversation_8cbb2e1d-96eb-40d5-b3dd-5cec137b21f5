import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';

/// ويدجت عرض نتائج الامتحانات المخصص
class ExamResultWidget extends StatelessWidget {
  final ResultData resultData;
  final ResultStateData resultState;
  final String userProfileUrl;

  const ExamResultWidget({
    super.key,
    required this.resultData,
    required this.resultState,
    required this.userProfileUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.surface.withOpacity(0.1),
            Theme.of(context).colorScheme.surface.withOpacity(0.05),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // رأس نتائج الامتحان
          _buildExamHeader(context),
          const SizedBox(height: 20),

          // صورة المستخدم
          _buildUserAvatar(context),
          const SizedBox(height: 20),

          // نتائج الامتحان الرئيسية
          _buildMainResults(context),
          const SizedBox(height: 20),

          // تفاصيل الامتحان
          _buildExamDetails(context),
        ],
      ),
    );
  }

  /// بناء رأس نتائج الامتحان
  Widget _buildExamHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            resultState.isWinner
                ? Colors.green.withOpacity(0.2)
                : Colors.orange.withOpacity(0.2),
            resultState.isWinner
                ? Colors.green.withOpacity(0.1)
                : Colors.orange.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            resultState.isWinner ? Icons.school : Icons.assignment_late,
            color: resultState.isWinner ? Colors.green : Colors.orange,
            size: 28,
          ),
          const SizedBox(width: 12),
          Column(
            children: [
              Text(
                resultData.exam?.title ?? 'الامتحان',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeights.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                resultState.isWinner ? 'نجح' : 'يحتاج مراجعة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeights.medium,
                  color: resultState.isWinner ? Colors.green : Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء صورة المستخدم
  Widget _buildUserAvatar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: (resultState.isWinner ? Colors.green : Colors.orange)
                .withOpacity(0.3),
            blurRadius: 10,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: QImage.circular(
        width: 80,
        height: 80,
        imageUrl: userProfileUrl,
      ),
    );
  }

  /// بناء النتائج الرئيسية
  Widget _buildMainResults(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // الدرجة المحصلة
          _buildScoreDisplay(context),
          const SizedBox(height: 20),

          // إحصائيات سريعة
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildQuickStat(
                context,
                icon: Icons.check_circle,
                label: 'صحيح',
                value:
                    '${resultData.correctExamAnswers ?? resultData.correctAnswers}',
                color: Colors.green,
              ),
              _buildQuickStat(
                context,
                icon: Icons.cancel,
                label: 'خطأ',
                value:
                    '${resultData.incorrectExamAnswers ?? (resultData.totalQuestions - resultData.correctAnswers)}',
                color: Colors.red,
              ),
              _buildQuickStat(
                context,
                icon: Icons.quiz,
                label: 'المجموع',
                value: '${resultData.totalQuestions}',
                color: Theme.of(context).primaryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء عرض الدرجة
  Widget _buildScoreDisplay(BuildContext context) {
    final obtainedMarks = resultData.obtainedMarks ?? 0;
    final totalMarks = (resultData.exam?.totalMarks as num?) ?? 100;
    final percentage =
        totalMarks > 0 ? (obtainedMarks / totalMarks) * 100 : 0.0;

    return Column(
      children: [
        Text(
          'الدرجة المحصلة',
          style: TextStyle(
            fontSize: 16,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 8),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '$obtainedMarks',
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeights.bold,
                  color: resultState.isWinner ? Colors.green : Colors.orange,
                ),
              ),
              TextSpan(
                text: ' / $totalMarks',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeights.medium,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${percentage.toStringAsFixed(1)}%',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeights.bold,
            color: resultState.isWinner ? Colors.green : Colors.orange,
          ),
        ),
      ],
    );
  }

  /// بناء إحصائية سريعة
  Widget _buildQuickStat(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeights.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      ],
    );
  }

  /// بناء تفاصيل الامتحان
  Widget _buildExamDetails(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // مدة الامتحان
          if (resultData.examCompletedInMinutes != null)
            _buildDetailRow(
              context,
              icon: Icons.timer,
              label: 'مدة الامتحان',
              value: '${resultData.examCompletedInMinutes} دقيقة',
            ),

          // تاريخ الامتحان (معلق مؤقتاً - startDate غير متوفر في نموذج Exam)
          // TODO: إضافة تاريخ الامتحان عند توفره في النموذج
          /*
          if (resultData.exam?.startDate != null) ...[
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              icon: Icons.calendar_today,
              label: 'تاريخ الامتحان',
              value: _formatDate(resultData.exam!.startDate!),
            ),
          ],
          */

          // نوع الامتحان
          const SizedBox(height: 12),
          _buildDetailRow(
            context,
            icon: Icons.category,
            label: 'نوع الامتحان',
            value: 'امتحان رسمي',
          ),
        ],
      ),
    );
  }

  /// بناء صف تفصيل
  Widget _buildDetailRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeights.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }

  /// تنسيق التاريخ (معلق مؤقتاً)
  // TODO: إعادة تفعيل عند إضافة تاريخ الامتحان
  /*
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
  */
}
