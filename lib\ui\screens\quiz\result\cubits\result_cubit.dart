import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/setContestLeaderboardCubit.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/statistic/cubits/updateStatisticCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/string_labels.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';

/// Cubit لإدارة حالة النتائج ومنطق الأعمال
class ResultCubit extends Cubit<ResultStateData> {
  final ResultData resultData;
  final BuildContext context;

  ResultCubit({
    required this.resultData,
    required this.context,
  }) : super(ResultStateData.initial());

  /// تهيئة النتائج وحساب جميع البيانات المطلوبة
  Future<void> initializeResults() async {
    try {
      emit(state.copyWith(isCalculating: true));

      // التحقق من صحة البيانات للاختبارات غير الامتحانية
      if (resultData.quizType != QuizTypes.exam) {
        if (resultData.questions == null) {
          emit(ResultStateData.error(
              'لا توجد أسئلة للمعالجة - questions is null'));
          return;
        }
        if (resultData.questions!.isEmpty) {
          emit(ResultStateData.error(
              'لا توجد أسئلة للمعالجة - questions list is empty'));
          return;
        }
      }

      // حساب عدد الإجابات الصحيحة
      final correctAnswersCount = _calculateCorrectAnswers();

      // حساب النتائج الأساسية
      final winPercentage = _calculateWinPercentage(correctAnswersCount);
      final requiredPercentage =
          context.read<SystemConfigCubit>().quizWinningPercentage;
      final isWinner = winPercentage >= requiredPercentage;

      print('initializeResults: winPercentage = $winPercentage');
      print('initializeResults: requiredPercentage = $requiredPercentage');
      print('initializeResults: isWinner = $isWinner');

      // حساب العملات (معلق حالياً)
      final earnedCoins = _calculateEarnedCoins(isWinner, winPercentage);

      // إنشاء الرسائل
      final mainMessage = ResultMessageGenerator.getMainMessage(isWinner);
      final subMessage = ResultMessageGenerator.getSubMessage(isWinner);
      final appbarTitle = _generateAppbarTitle();

      print('initializeResults: earnedCoins = $earnedCoins');
      print('initializeResults: mainMessage = "$mainMessage"');
      print('initializeResults: About to emit final state...');

      // تحديث الحالة
      emit(ResultStateData(
        isWinner: isWinner,
        earnedCoins: earnedCoins,
        winPercentage: winPercentage,
        correctAnswers: correctAnswersCount,
        totalQuestions: _totalQuestions(),
        mainMessage: mainMessage,
        subMessage: subMessage,
        appbarTitle: appbarTitle,
        isCalculating: false,
      ));

      print('initializeResults: Final state emitted successfully!');

      // تنفيذ العمليات الجانبية
      await _performSideEffects(isWinner, earnedCoins);
    } catch (e) {
      emit(ResultStateData.error('حدث خطأ في حساب النتائج: $e'));
    }
  }

  /// حساب عدد الإجابات الصحيحة
  int _calculateCorrectAnswers() {
    if (resultData.quizType == QuizTypes.exam) {
      return resultData.correctExamAnswers ?? 0;
    }

    // التحقق من وجود الأسئلة
    if (resultData.questions == null || resultData.questions!.isEmpty) {
      print('Warning: No questions found for calculating correct answers');
      return 0;
    }

    print(
        '_calculateCorrectAnswers: Starting with ${resultData.questions!.length} questions');

    final userFirebaseId = context.read<UserDetailsCubit>().getUserFirebaseId();
    print('_calculateCorrectAnswers: userFirebaseId = "$userFirebaseId"');

    if (userFirebaseId.isEmpty) {
      print('ERROR: userFirebaseId is empty!');
      return 0;
    }

    var correctAnswersCount = 0;
    for (int i = 0; i < resultData.questions!.length; i++) {
      final question = resultData.questions![i];
      print('_calculateCorrectAnswers: Question $i - ID: ${question.id}');
      print(
          '_calculateCorrectAnswers: Question $i - submittedAnswerId: "${question.submittedAnswerId}"');
      print(
          '_calculateCorrectAnswers: Question $i - correctAnswer: ${question.correctAnswer != null ? "exists" : "null"}');

      if (question.correctAnswer == null) {
        print('ERROR: Question $i has null correctAnswer!');
        continue;
      }

      try {
        final decryptedAnswer = AnswerEncryption.decryptCorrectAnswer(
          rawKey: userFirebaseId,
          correctAnswer: question.correctAnswer!,
        );
        print(
            '_calculateCorrectAnswers: Question $i - decryptedAnswer: "$decryptedAnswer"');

        if (decryptedAnswer == question.submittedAnswerId) {
          correctAnswersCount++;
          print(
              '_calculateCorrectAnswers: Question $i - CORRECT! Total: $correctAnswersCount');
        } else {
          print('_calculateCorrectAnswers: Question $i - WRONG');
        }
      } catch (e) {
        print('ERROR: Failed to decrypt answer for question $i: $e');
        continue;
      }
    }

    print('_calculateCorrectAnswers: Final result = $correctAnswersCount');
    return correctAnswersCount;
  }

  /// حساب نسبة الفوز
  double _calculateWinPercentage(int correctAnswers) {
    print('_calculateWinPercentage: correctAnswers = $correctAnswers');
    print('_calculateWinPercentage: quizType = ${resultData.quizType}');

    if (resultData.quizType == QuizTypes.oneVsOneBattle) {
      print('_calculateWinPercentage: Battle mode, returning 0');
      return 0;
    }

    if (resultData.quizType == QuizTypes.exam) {
      final obtainedMarks = resultData.obtainedMarks!;
      final totalMarks = int.parse(resultData.exam!.totalMarks);
      final percentage = (obtainedMarks * 100.0) / totalMarks;
      print(
          '_calculateWinPercentage: Exam mode - $obtainedMarks/$totalMarks = $percentage%');
      return percentage;
    }

    final totalQuestions = _totalQuestions();
    final percentage = (correctAnswers * 100.0) / totalQuestions;
    print(
        '_calculateWinPercentage: Quiz mode - $correctAnswers/$totalQuestions = $percentage%');
    return percentage;
  }

  /// حساب إجمالي الأسئلة (مثل الكود الأصلي)
  int _totalQuestions() {
    print('_totalQuestions: quizType = ${resultData.quizType}');

    if (resultData.quizType == QuizTypes.exam) {
      final correct = resultData.correctExamAnswers ?? 0;
      final incorrect = resultData.incorrectExamAnswers ?? 0;
      final total = correct + incorrect;
      print('_totalQuestions: Exam mode - $correct + $incorrect = $total');
      return total;
    }

    // التحقق من وجود الأسئلة
    if (resultData.questions == null || resultData.questions!.isEmpty) {
      print('ERROR: No questions found for calculating total questions');
      return 0;
    }

    final questionsLength = resultData.questions!.length;
    print('_totalQuestions: questions.length = $questionsLength');
    print('_totalQuestions: didSkipQuestions = ${resultData.didSkipQuestions}');

    // التحقق من تخطي الأسئلة في QuizZone
    if (resultData.didSkipQuestions) {
      final total = questionsLength - 1;
      print('_totalQuestions: With skip - $questionsLength - 1 = $total');
      return total;
    }

    print('_totalQuestions: Final total = $questionsLength');
    return questionsLength;
  }

  /// حساب العملات المكتسبة (معلق حالياً للنموذج الاشتراكي)
  int _calculateEarnedCoins(bool isWinner, double winPercentage) {
    // TODO: تم تعليق نظام العملات مؤقتاً
    // يمكن إعادة تفعيله لاحقاً عند الحاجة

    /*
    if (!isWinner) return 0;
    
    // حساب العملات بناءً على نسبة النجاح
    final baseCoins = context.read<SystemConfigCubit>().quizWinningCoins;
    final bonusMultiplier = winPercentage / 100;
    return (baseCoins * bonusMultiplier).round();
    */

    return 0; // معلق حالياً
  }

  /// إنشاء عنوان شريط التطبيق
  String _generateAppbarTitle() {
    final (title, emoji) = switch (resultData.quizType) {
      QuizTypes.selfChallenge => ('selfChallengeResult', '🎯'),
      QuizTypes.exam => ('examResult', '📝'),
      QuizTypes.dailyQuiz => ('dailyQuizResult', '📅'),
      QuizTypes.oneVsOneBattle => ('randomBattleResult', '⚔️'),
      QuizTypes.funAndLearn => ('funAndLearnResult', '🎮'),
      QuizTypes.bookmarkQuiz => ('bookmarkQuizResult', '🔖'),
      _ => ('quizResultLbl', '🏆'),
    };

    return '$emoji ${context.tr(title)!}';
  }

  /// تنفيذ العمليات الجانبية (الشارات، الإحصائيات، إلخ)
  Future<void> _performSideEffects(bool isWinner, int earnedCoins) async {
    try {
      print('_performSideEffects: Starting...');

      // كسب الشارات
      print('_performSideEffects: Earning badges...');
      await _earnBadges();
      print('_performSideEffects: Badges earned successfully');

      // تحديث النقاط والعملات
      print('_performSideEffects: Updating score and coins...');
      await _updateScoreAndCoins(earnedCoins);
      print('_performSideEffects: Score and coins updated successfully');

      // تحديث الإحصائيات
      print('_performSideEffects: Updating statistics...');
      await _updateStatistics();
      print('_performSideEffects: Statistics updated successfully');

      // تحديث قائمة المتصدرين للمسابقات
      print('_performSideEffects: Setting contest leaderboard...');
      await _setContestLeaderboard();
      print('_performSideEffects: Contest leaderboard set successfully');

      print('_performSideEffects: All side effects completed successfully');
    } catch (e) {
      print('ERROR in _performSideEffects: $e');
      rethrow;
    }
  }

  /// كسب الشارات
  Future<void> _earnBadges() async {
    if (resultData.quizType == QuizTypes.oneVsOneBattle) {
      await _earnBattleBadges();
    } else if (resultData.quizType == QuizTypes.quizZone) {
      await _earnQuizZoneBadges();
    }
    // يمكن إضافة المزيد من أنواع الشارات هنا
  }

  /// كسب شارات المعارك
  Future<void> _earnBattleBadges() async {
    final userId = context.read<UserDetailsCubit>().userId();
    final badgesCubit = context.read<BadgesCubit>();

    final languageId = UiUtils.getCurrentQuizLanguageId(context);

    // شارة اللاعب المطلق
    if (badgesCubit.isBadgeLocked('ultimate_player')) {
      // استخدام قيمة افتراضية إذا لم تكن متوفرة
      final badgeEarnPoints = 100; // قيمة افتراضية
      final currentUserPoints = resultData.battleRoom?.user1?.uid == userId
          ? resultData.battleRoom?.user1?.points ?? 0
          : resultData.battleRoom?.user2?.points ?? 0;

      if (currentUserPoints >= badgeEarnPoints) {
        badgesCubit.setBadge(
          badgeType: 'ultimate_player',
          languageId: languageId,
        );
      }
    }

    // شارة الفوز الكبير
    if (badgesCubit.isBadgeLocked('big_thing')) {
      final requiredScore = 150; // قيمة افتراضية
      final currentUserPoints = resultData.battleRoom?.user1?.uid == userId
          ? resultData.battleRoom?.user1?.points ?? 0
          : resultData.battleRoom?.user2?.points ?? 0;

      if (currentUserPoints >= requiredScore) {
        badgesCubit.setBadge(
          badgeType: 'big_thing',
          languageId: languageId,
        );
      }
    }
  }

  /// كسب شارات منطقة الاختبار
  Future<void> _earnQuizZoneBadges() async {
    try {
      print('_earnQuizZoneBadges: Starting...');

      print('_earnQuizZoneBadges: Getting BadgesCubit...');
      final badgesCubit = context.read<BadgesCubit>();
      print('_earnQuizZoneBadges: BadgesCubit obtained successfully');

      print('_earnQuizZoneBadges: Getting language ID...');
      final languageId = UiUtils.getCurrentQuizLanguageId(context);
      print('_earnQuizZoneBadges: Language ID = $languageId');

      // شارة الإجابة الصحيحة الأولى
      print('_earnQuizZoneBadges: Checking bravo badge...');
      print(
          '_earnQuizZoneBadges: state.correctAnswers = ${state.correctAnswers}');
      print(
          '_earnQuizZoneBadges: hasUsedAnyLifeline = ${resultData.hasUsedAnyLifeline}');

      print('_earnQuizZoneBadges: Calling isBadgeLocked for bravo...');
      final isBravoLocked = badgesCubit.isBadgeLocked('bravo');
      print('_earnQuizZoneBadges: isBadgeLocked(bravo) = $isBravoLocked');

      if (isBravoLocked &&
          state.correctAnswers > 0 &&
          resultData.hasUsedAnyLifeline == false) {
        print('_earnQuizZoneBadges: Setting bravo badge...');
        badgesCubit.setBadge(
          badgeType: 'bravo',
          languageId: languageId,
        );
        print('_earnQuizZoneBadges: Bravo badge set successfully');
      }

      // شارة الإكمال بدون استخدام المساعدات
      print('_earnQuizZoneBadges: Checking extra_ordinary badge...');
      print('_earnQuizZoneBadges: state.isWinner = ${state.isWinner}');

      print('_earnQuizZoneBadges: Calling isBadgeLocked for extra_ordinary...');
      final isExtraOrdinaryLocked = badgesCubit.isBadgeLocked('extra_ordinary');
      print(
          '_earnQuizZoneBadges: isBadgeLocked(extra_ordinary) = $isExtraOrdinaryLocked');

      if (isExtraOrdinaryLocked &&
          state.isWinner &&
          resultData.hasUsedAnyLifeline == false) {
        print('_earnQuizZoneBadges: Setting extra_ordinary badge...');
        badgesCubit.setBadge(
          badgeType: 'extra_ordinary',
          languageId: languageId,
        );
        print('_earnQuizZoneBadges: Extra ordinary badge set successfully');
      }

      print('_earnQuizZoneBadges: Completed successfully');
    } catch (e) {
      print('ERROR in _earnQuizZoneBadges: $e');
      rethrow;
    }
  }

  /// تحديث النقاط والعملات
  Future<void> _updateScoreAndCoins(int earnedCoins) async {
    if (resultData.quizType == QuizTypes.oneVsOneBattle) {
      await _updateBattleScoreAndCoins(earnedCoins);
    } else {
      await _updateRegularScoreAndCoins(earnedCoins);
    }
  }

  /// تحديث نقاط وعملات المعارك
  Future<void> _updateBattleScoreAndCoins(int earnedCoins) async {
    final currentUserId = context.read<UserDetailsCubit>().userId();
    final currentUser = resultData.battleRoom!.user1!.uid == currentUserId
        ? resultData.battleRoom!.user1!
        : resultData.battleRoom!.user2!;

    if (state.isWinner && !resultData.playWithBot) {
      // تحديث النقاط والعملات للفائز
      context.read<UpdateScoreAndCoinsCubit>().updateCoinsAndScore(
            currentUser.points,
            earnedCoins,
            wonBattleKey,
          );

      // تحديث البيانات محلياً
      context.read<UserDetailsCubit>().updateCoins(
            addCoin: true,
            coins: earnedCoins,
          );
      context.read<UserDetailsCubit>().updateScore(currentUser.points);
    } else if (!resultData.playWithBot) {
      // تحديث النقاط فقط للخاسر
      context.read<UpdateScoreAndCoinsCubit>().updateScore(currentUser.points);
      context.read<UserDetailsCubit>().updateScore(currentUser.points);
    }
  }

  /// تحديث نقاط وعملات الاختبارات العادية
  Future<void> _updateRegularScoreAndCoins(int earnedCoins) async {
    if (resultData.quizType == QuizTypes.selfChallenge ||
        resultData.quizType == QuizTypes.exam) {
      return; // لا نحدث النقاط في هذه الأنواع
    }

    final points = resultData.myPoints ?? 0;

    if (state.isWinner && earnedCoins > 0) {
      context.read<UpdateScoreAndCoinsCubit>().updateCoinsAndScore(
            points,
            earnedCoins,
            _getWinKey(),
          );

      context.read<UserDetailsCubit>().updateCoins(
            addCoin: true,
            coins: earnedCoins,
          );
    } else {
      context.read<UpdateScoreAndCoinsCubit>().updateScore(points);
    }

    context.read<UserDetailsCubit>().updateScore(points);
  }

  /// الحصول على مفتاح الفوز حسب نوع الاختبار
  String _getWinKey() {
    return switch (resultData.quizType) {
      QuizTypes.dailyQuiz => wonDailyQuizKey,
      QuizTypes.funAndLearn => wonQuizZoneKey, // استخدام مفتاح موجود
      QuizTypes.quizZone => wonQuizZoneKey,
      QuizTypes.audioQuestions => wonQuizZoneKey, // استخدام مفتاح موجود
      QuizTypes.contest => wonContestKey,
      _ => wonQuizZoneKey,
    };
  }

  /// تحديث الإحصائيات
  Future<void> _updateStatistics() async {
    if (resultData.quizType == QuizTypes.selfChallenge ||
        resultData.quizType == QuizTypes.exam) {
      return;
    }

    context.read<UpdateStatisticCubit>().updateStatistic(
          answeredQuestion: resultData.attemptedQuestions,
          categoryId: resultData.getCategoryId(),
          correctAnswers: state.correctAnswers,
          winPercentage: state.winPercentage,
        );

    // تحديث إحصائيات المعارك
    if (resultData.quizType == QuizTypes.oneVsOneBattle &&
        !resultData.playWithBot) {
      await _updateBattleStatistics();
    }
  }

  /// تحديث إحصائيات المعارك
  Future<void> _updateBattleStatistics() async {
    final currentUserId = context.read<UserDetailsCubit>().userId();

    context.read<UpdateStatisticCubit>().updateBattleStatistic(
          userId1: currentUserId == resultData.battleRoom!.user1!.uid
              ? resultData.battleRoom!.user1!.uid
              : resultData.battleRoom!.user2!.uid,
          userId2: resultData.battleRoom!.user1!.uid != currentUserId
              ? resultData.battleRoom!.user1!.uid
              : resultData.battleRoom!.user2!.uid,
          winnerId: state.winnerId ?? '',
        );
  }

  /// تحديث قائمة المتصدرين للمسابقات
  Future<void> _setContestLeaderboard() async {
    if (resultData.contestId != null && resultData.contestId!.isNotEmpty) {
      context.read<SetContestLeaderboardCubit>().setContestLeaderboard(
            contestId: resultData.contestId!,
            score: resultData.myPoints ?? 0,
          );
    }
  }

  /// تحديث حالة عرض نافذة تسجيل الدخول
  void updateAlreadyLoggedInDialogState(bool displayed) {
    emit(state.copyWith(displayedAlreadyLoggedInDialog: displayed));
  }
}
