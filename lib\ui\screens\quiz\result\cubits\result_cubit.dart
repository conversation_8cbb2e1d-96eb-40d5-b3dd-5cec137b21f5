import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/setContestLeaderboardCubit.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/statistic/cubits/updateStatisticCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/string_labels.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';

/// Cubit لإدارة حالة النتائج ومنطق الأعمال
class ResultCubit extends Cubit<ResultStateData> {
  final ResultData resultData;
  final BuildContext context;

  ResultCubit({
    required this.resultData,
    required this.context,
  }) : super(ResultStateData.initial());

  /// تهيئة النتائج وحساب جميع البيانات المطلوبة
  Future<void> initializeResults() async {
    try {
      emit(state.copyWith(isCalculating: true));

      // التحقق من صحة البيانات للاختبارات غير الامتحانية
      if (resultData.quizType != QuizTypes.exam) {
        if (resultData.questions == null) {
          emit(ResultStateData.error(
              'لا توجد أسئلة للمعالجة - questions is null'));
          return;
        }
        if (resultData.questions!.isEmpty) {
          emit(ResultStateData.error(
              'لا توجد أسئلة للمعالجة - questions list is empty'));
          return;
        }
      }

      // حساب عدد الإجابات الصحيحة
      final correctAnswersCount = _calculateCorrectAnswers();

      // حساب النتائج الأساسية
      final winPercentage = _calculateWinPercentage(correctAnswersCount);
      final requiredPercentage =
          context.read<SystemConfigCubit>().quizWinningPercentage;
      final isWinner = winPercentage >= requiredPercentage;

      // حساب العملات (معلق حالياً)
      final earnedCoins = _calculateEarnedCoins(isWinner, winPercentage);

      // إنشاء الرسائل
      final mainMessage = ResultMessageGenerator.getMainMessage(isWinner);
      final subMessage = ResultMessageGenerator.getSubMessage(isWinner);
      final appbarTitle = _generateAppbarTitle();

      // تحديث الحالة
      emit(ResultStateData(
        isWinner: isWinner,
        earnedCoins: earnedCoins,
        winPercentage: winPercentage,
        correctAnswers: correctAnswersCount,
        totalQuestions: _totalQuestions(),
        mainMessage: mainMessage,
        subMessage: subMessage,
        appbarTitle: appbarTitle,
        isCalculating: false,
      ));

      // تنفيذ العمليات الجانبية
      await _performSideEffects(isWinner, earnedCoins);
    } catch (e) {
      emit(ResultStateData.error('حدث خطأ في حساب النتائج: $e'));
    }
  }

  /// حساب عدد الإجابات الصحيحة
  int _calculateCorrectAnswers() {
    if (resultData.quizType == QuizTypes.exam) {
      return resultData.correctExamAnswers ?? 0;
    }

    // التحقق من وجود الأسئلة
    if (resultData.questions == null || resultData.questions!.isEmpty) {
      print('Warning: No questions found for calculating correct answers');
      return 0;
    }

    var correctAnswersCount = 0;
    for (final question in resultData.questions!) {
      if (AnswerEncryption.decryptCorrectAnswer(
            rawKey: context.read<UserDetailsCubit>().getUserFirebaseId(),
            correctAnswer: question.correctAnswer!,
          ) ==
          question.submittedAnswerId) {
        correctAnswersCount++;
      }
    }
    return correctAnswersCount;
  }

  /// حساب نسبة الفوز
  double _calculateWinPercentage(int correctAnswers) {
    if (resultData.quizType == QuizTypes.oneVsOneBattle) return 0;

    if (resultData.quizType == QuizTypes.exam) {
      return (resultData.obtainedMarks! * 100.0) /
          int.parse(resultData.exam!.totalMarks);
    }

    return (correctAnswers * 100.0) / _totalQuestions();
  }

  /// حساب إجمالي الأسئلة (مثل الكود الأصلي)
  int _totalQuestions() {
    if (resultData.quizType == QuizTypes.exam) {
      return (resultData.correctExamAnswers ?? 0) +
          (resultData.incorrectExamAnswers ?? 0);
    }

    // التحقق من وجود الأسئلة
    if (resultData.questions == null || resultData.questions!.isEmpty) {
      print('Warning: No questions found for calculating total questions');
      return 0;
    }

    // التحقق من تخطي الأسئلة في QuizZone
    if (resultData.didSkipQuestions) {
      return resultData.questions!.length - 1;
    }

    return resultData.questions!.length;
  }

  /// حساب العملات المكتسبة (معلق حالياً للنموذج الاشتراكي)
  int _calculateEarnedCoins(bool isWinner, double winPercentage) {
    // TODO: تم تعليق نظام العملات مؤقتاً
    // يمكن إعادة تفعيله لاحقاً عند الحاجة

    /*
    if (!isWinner) return 0;
    
    // حساب العملات بناءً على نسبة النجاح
    final baseCoins = context.read<SystemConfigCubit>().quizWinningCoins;
    final bonusMultiplier = winPercentage / 100;
    return (baseCoins * bonusMultiplier).round();
    */

    return 0; // معلق حالياً
  }

  /// إنشاء عنوان شريط التطبيق
  String _generateAppbarTitle() {
    final (title, emoji) = switch (resultData.quizType) {
      QuizTypes.selfChallenge => ('selfChallengeResult', '🎯'),
      QuizTypes.exam => ('examResult', '📝'),
      QuizTypes.dailyQuiz => ('dailyQuizResult', '📅'),
      QuizTypes.oneVsOneBattle => ('randomBattleResult', '⚔️'),
      QuizTypes.funAndLearn => ('funAndLearnResult', '🎮'),
      QuizTypes.bookmarkQuiz => ('bookmarkQuizResult', '🔖'),
      _ => ('quizResultLbl', '🏆'),
    };

    return '$emoji ${context.tr(title)!}';
  }

  /// تنفيذ العمليات الجانبية (الشارات، الإحصائيات، إلخ)
  Future<void> _performSideEffects(bool isWinner, int earnedCoins) async {
    // كسب الشارات
    await _earnBadges();

    // تحديث النقاط والعملات
    await _updateScoreAndCoins(earnedCoins);

    // تحديث الإحصائيات
    await _updateStatistics();

    // تحديث قائمة المتصدرين للمسابقات
    await _setContestLeaderboard();
  }

  /// كسب الشارات
  Future<void> _earnBadges() async {
    if (resultData.quizType == QuizTypes.oneVsOneBattle) {
      await _earnBattleBadges();
    } else if (resultData.quizType == QuizTypes.quizZone) {
      await _earnQuizZoneBadges();
    }
    // يمكن إضافة المزيد من أنواع الشارات هنا
  }

  /// كسب شارات المعارك
  Future<void> _earnBattleBadges() async {
    final userId = context.read<UserDetailsCubit>().userId();
    final badgesCubit = context.read<BadgesCubit>();

    final languageId = UiUtils.getCurrentQuizLanguageId(context);

    // شارة اللاعب المطلق
    if (badgesCubit.isBadgeLocked('ultimate_player')) {
      // استخدام قيمة افتراضية إذا لم تكن متوفرة
      final badgeEarnPoints = 100; // قيمة افتراضية
      final currentUserPoints = resultData.battleRoom?.user1?.uid == userId
          ? resultData.battleRoom?.user1?.points ?? 0
          : resultData.battleRoom?.user2?.points ?? 0;

      if (currentUserPoints >= badgeEarnPoints) {
        badgesCubit.setBadge(
          badgeType: 'ultimate_player',
          languageId: languageId,
        );
      }
    }

    // شارة الفوز الكبير
    if (badgesCubit.isBadgeLocked('big_thing')) {
      final requiredScore = 150; // قيمة افتراضية
      final currentUserPoints = resultData.battleRoom?.user1?.uid == userId
          ? resultData.battleRoom?.user1?.points ?? 0
          : resultData.battleRoom?.user2?.points ?? 0;

      if (currentUserPoints >= requiredScore) {
        badgesCubit.setBadge(
          badgeType: 'big_thing',
          languageId: languageId,
        );
      }
    }
  }

  /// كسب شارات منطقة الاختبار
  Future<void> _earnQuizZoneBadges() async {
    final badgesCubit = context.read<BadgesCubit>();
    final languageId = UiUtils.getCurrentQuizLanguageId(context);

    // شارة الإجابة الصحيحة الأولى
    if (badgesCubit.isBadgeLocked('bravo') &&
        resultData.correctAnswers > 0 &&
        resultData.hasUsedAnyLifeline == false) {
      badgesCubit.setBadge(
        badgeType: 'bravo',
        languageId: languageId,
      );
    }

    // شارة الإكمال بدون استخدام المساعدات
    if (badgesCubit.isBadgeLocked('extra_ordinary') &&
        state.isWinner &&
        resultData.hasUsedAnyLifeline == false) {
      badgesCubit.setBadge(
        badgeType: 'extra_ordinary',
        languageId: languageId,
      );
    }
  }

  /// تحديث النقاط والعملات
  Future<void> _updateScoreAndCoins(int earnedCoins) async {
    if (resultData.quizType == QuizTypes.oneVsOneBattle) {
      await _updateBattleScoreAndCoins(earnedCoins);
    } else {
      await _updateRegularScoreAndCoins(earnedCoins);
    }
  }

  /// تحديث نقاط وعملات المعارك
  Future<void> _updateBattleScoreAndCoins(int earnedCoins) async {
    final currentUserId = context.read<UserDetailsCubit>().userId();
    final currentUser = resultData.battleRoom!.user1!.uid == currentUserId
        ? resultData.battleRoom!.user1!
        : resultData.battleRoom!.user2!;

    if (state.isWinner && !resultData.playWithBot) {
      // تحديث النقاط والعملات للفائز
      context.read<UpdateScoreAndCoinsCubit>().updateCoinsAndScore(
            currentUser.points,
            earnedCoins,
            wonBattleKey,
          );

      // تحديث البيانات محلياً
      context.read<UserDetailsCubit>().updateCoins(
            addCoin: true,
            coins: earnedCoins,
          );
      context.read<UserDetailsCubit>().updateScore(currentUser.points);
    } else if (!resultData.playWithBot) {
      // تحديث النقاط فقط للخاسر
      context.read<UpdateScoreAndCoinsCubit>().updateScore(currentUser.points);
      context.read<UserDetailsCubit>().updateScore(currentUser.points);
    }
  }

  /// تحديث نقاط وعملات الاختبارات العادية
  Future<void> _updateRegularScoreAndCoins(int earnedCoins) async {
    if (resultData.quizType == QuizTypes.selfChallenge ||
        resultData.quizType == QuizTypes.exam) {
      return; // لا نحدث النقاط في هذه الأنواع
    }

    final points = resultData.myPoints ?? 0;

    if (state.isWinner && earnedCoins > 0) {
      context.read<UpdateScoreAndCoinsCubit>().updateCoinsAndScore(
            points,
            earnedCoins,
            _getWinKey(),
          );

      context.read<UserDetailsCubit>().updateCoins(
            addCoin: true,
            coins: earnedCoins,
          );
    } else {
      context.read<UpdateScoreAndCoinsCubit>().updateScore(points);
    }

    context.read<UserDetailsCubit>().updateScore(points);
  }

  /// الحصول على مفتاح الفوز حسب نوع الاختبار
  String _getWinKey() {
    return switch (resultData.quizType) {
      QuizTypes.dailyQuiz => wonDailyQuizKey,
      QuizTypes.funAndLearn => wonQuizZoneKey, // استخدام مفتاح موجود
      QuizTypes.quizZone => wonQuizZoneKey,
      QuizTypes.audioQuestions => wonQuizZoneKey, // استخدام مفتاح موجود
      QuizTypes.contest => wonContestKey,
      _ => wonQuizZoneKey,
    };
  }

  /// تحديث الإحصائيات
  Future<void> _updateStatistics() async {
    if (resultData.quizType == QuizTypes.selfChallenge ||
        resultData.quizType == QuizTypes.exam) {
      return;
    }

    context.read<UpdateStatisticCubit>().updateStatistic(
          answeredQuestion: resultData.attemptedQuestions,
          categoryId: resultData.getCategoryId(),
          correctAnswers: resultData.correctAnswers,
          winPercentage: state.winPercentage,
        );

    // تحديث إحصائيات المعارك
    if (resultData.quizType == QuizTypes.oneVsOneBattle &&
        !resultData.playWithBot) {
      await _updateBattleStatistics();
    }
  }

  /// تحديث إحصائيات المعارك
  Future<void> _updateBattleStatistics() async {
    final currentUserId = context.read<UserDetailsCubit>().userId();

    context.read<UpdateStatisticCubit>().updateBattleStatistic(
          userId1: currentUserId == resultData.battleRoom!.user1!.uid
              ? resultData.battleRoom!.user1!.uid
              : resultData.battleRoom!.user2!.uid,
          userId2: resultData.battleRoom!.user1!.uid != currentUserId
              ? resultData.battleRoom!.user1!.uid
              : resultData.battleRoom!.user2!.uid,
          winnerId: state.winnerId ?? '',
        );
  }

  /// تحديث قائمة المتصدرين للمسابقات
  Future<void> _setContestLeaderboard() async {
    if (resultData.contestId != null && resultData.contestId!.isNotEmpty) {
      context.read<SetContestLeaderboardCubit>().setContestLeaderboard(
            contestId: resultData.contestId!,
            score: resultData.myPoints ?? 0,
          );
    }
  }

  /// تحديث حالة عرض نافذة تسجيل الدخول
  void updateAlreadyLoggedInDialogState(bool displayed) {
    emit(state.copyWith(displayedAlreadyLoggedInDialog: displayed));
  }
}
