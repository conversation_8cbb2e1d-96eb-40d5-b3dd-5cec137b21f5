import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/radialResultContainer.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';

/// ويدجت عرض نتائج اللاعب الفردي
class IndividualResultWidget extends StatelessWidget {
  final ResultData resultData;
  final ResultStateData resultState;
  final String userProfileUrl;

  const IndividualResultWidget({
    super.key,
    required this.resultData,
    required this.resultState,
    required this.userProfileUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.surface.withOpacity(0.1),
            Theme.of(context).colorScheme.surface.withOpacity(0.05),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // صورة المستخدم
          _buildUserAvatar(context),
          const SizedBox(height: 20),

          // دائرة النتائج
          _buildResultCircle(context),
          const SizedBox(height: 20),

          // تفاصيل النتائج
          _buildResultDetails(context),
        ],
      ),
    );
  }

  /// بناء صورة المستخدم
  Widget _buildUserAvatar(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 10,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: QImage.circular(
        width: 80,
        height: 80,
        imageUrl: userProfileUrl,
      ),
    );
  }

  /// بناء دائرة النتائج
  Widget _buildResultCircle(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return RadialPercentageResultContainer(
      percentage: resultState.winPercentage,
      size: Size(size.width * 0.5, size.width * 0.5),
      timeTakenToCompleteQuizInSeconds:
          resultData.timeTakenToCompleteQuiz?.toInt(),
      arcColor: resultState.isWinner
          ? const Color(0xFF4CAF50)
          : Theme.of(context).primaryColor,
      circleColor: Theme.of(context).colorScheme.surface.withOpacity(0.1),
    );
  }

  /// بناء تفاصيل النتائج
  Widget _buildResultDetails(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // الإجابات الصحيحة
          _buildStatRow(
            context,
            icon: Icons.check_circle,
            label: 'الإجابات الصحيحة',
            value: '${resultState.correctAnswers}',
            color: Colors.green,
          ),
          const SizedBox(height: 12),

          // الإجابات الخاطئة
          _buildStatRow(
            context,
            icon: Icons.cancel,
            label: 'الإجابات الخاطئة',
            value: '${resultData.totalQuestions - resultState.correctAnswers}',
            color: Colors.red,
          ),
          const SizedBox(height: 12),

          // إجمالي الأسئلة
          _buildStatRow(
            context,
            icon: Icons.quiz,
            label: 'إجمالي الأسئلة',
            value: '${resultData.totalQuestions}',
            color: Theme.of(context).primaryColor,
          ),

          // عرض العملات (معلق حالياً للنموذج الاشتراكي)
          // TODO: يمكن إعادة تفعيل هذا القسم عند الحاجة لنظام العملات
          /*
          if (resultState.earnedCoins > 0) ...[
            const SizedBox(height: 12),
            _buildStatRow(
              context,
              icon: Icons.monetization_on,
              label: context.tr('earnedCoins')!,
              value: '${resultState.earnedCoins}',
              color: Colors.amber,
            ),
          ],
          */
        ],
      ),
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeights.medium,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeights.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
