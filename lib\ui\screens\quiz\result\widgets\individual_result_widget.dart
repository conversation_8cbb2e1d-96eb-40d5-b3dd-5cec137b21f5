import 'package:flutter/material.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_data.dart';
import 'package:flutterquiz/ui/screens/quiz/result/models/result_state_data.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/radialResultContainer.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/utils/constants/fonts.dart';

/// ويدجت عرض نتائج اللاعب الفردي
class IndividualResultWidget extends StatelessWidget {
  final ResultData resultData;
  final ResultStateData resultState;
  final String userProfileUrl;

  const IndividualResultWidget({
    super.key,
    required this.resultData,
    required this.resultState,
    required this.userProfileUrl,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeOutBack,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.surface.withOpacity(0.95),
            Theme.of(context).colorScheme.surface.withOpacity(0.85),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 0.6, 1.0],
        ),
        borderRadius: BorderRadius.circular(28),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 2,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.05),
            blurRadius: 40,
            spreadRadius: 0,
            offset: const Offset(0, 16),
          ),
        ],
      ),
      child: Column(
        children: [
          // صورة المستخدم
          _buildUserAvatar(context),
          const SizedBox(height: 20),

          // دائرة النتائج
          _buildResultCircle(context),
          const SizedBox(height: 20),

          // تفاصيل النتائج
          _buildResultDetails(context),
        ],
      ),
    );
  }

  /// بناء صورة المستخدم
  Widget _buildUserAvatar(BuildContext context) {
    return Hero(
      tag: 'user_avatar_result',
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 600),
        curve: Curves.elasticOut,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.3),
              Theme.of(context).primaryColor.withOpacity(0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.4),
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withOpacity(0.2),
              blurRadius: 15,
              spreadRadius: 2,
              offset: const Offset(0, 6),
            ),
            BoxShadow(
              color: Theme.of(context).shadowColor.withOpacity(0.1),
              blurRadius: 25,
              spreadRadius: 0,
              offset: const Offset(0, 12),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: QImage.circular(
            width: 85,
            height: 85,
            imageUrl: userProfileUrl,
          ),
        ),
      ),
    );
  }

  /// بناء دائرة النتائج
  Widget _buildResultCircle(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return RadialPercentageResultContainer(
      percentage: resultState.winPercentage,
      size: Size(size.width * 0.5, size.width * 0.5),
      timeTakenToCompleteQuizInSeconds:
          resultData.timeTakenToCompleteQuiz?.toInt(),
      arcColor: resultState.isWinner
          ? const Color(0xFF4CAF50)
          : Theme.of(context).primaryColor,
      circleColor: Theme.of(context).colorScheme.surface.withOpacity(0.1),
    );
  }

  /// بناء تفاصيل النتائج
  Widget _buildResultDetails(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 700),
      curve: Curves.easeOutCubic,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.surface.withOpacity(0.8),
            Theme.of(context).colorScheme.surface.withOpacity(0.6),
            Theme.of(context).primaryColor.withOpacity(0.03),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          stops: const [0.0, 0.7, 1.0],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.15),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.08),
            blurRadius: 15,
            spreadRadius: 1,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          // الإجابات الصحيحة
          _buildStatRow(
            context,
            icon: Icons.check_circle,
            label: 'الإجابات الصحيحة',
            value: '${resultState.correctAnswers}',
            color: Colors.green,
          ),
          const SizedBox(height: 12),

          // الإجابات الخاطئة
          _buildStatRow(
            context,
            icon: Icons.cancel,
            label: 'الإجابات الخاطئة',
            value: '${resultState.totalQuestions - resultState.correctAnswers}',
            color: Colors.red,
          ),
          const SizedBox(height: 12),

          // إجمالي الأسئلة
          _buildStatRow(
            context,
            icon: Icons.quiz,
            label: 'إجمالي الأسئلة',
            value: '${resultState.totalQuestions}',
            color: Theme.of(context).primaryColor,
          ),

          // عرض العملات (معلق حالياً للنموذج الاشتراكي)
          // TODO: يمكن إعادة تفعيل هذا القسم عند الحاجة لنظام العملات
          /*
          if (resultState.earnedCoins > 0) ...[
            const SizedBox(height: 12),
            _buildStatRow(
              context,
              icon: Icons.monetization_on,
              label: context.tr('earnedCoins')!,
              value: '${resultState.earnedCoins}',
              color: Colors.amber,
            ),
          ],
          */
        ],
      ),
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOutBack,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withOpacity(0.08),
            color.withOpacity(0.03),
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  color.withOpacity(0.2),
                  color.withOpacity(0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: color,
              size: 22,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeights.medium,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeights.bold,
            color: color,
          ),
        ),
      ],
    ),
    );
  }
}
